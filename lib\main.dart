import 'package:bot_toast/bot_toast.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/controllers/bluetooth_printer_controller.dart';
import 'package:gls_self_order/core/controllers/usb_printer_controller.dart';
import 'package:gls_self_order/general/controllers/auth_controller.dart';
import 'package:gls_self_order/general/controllers/customer_controller.dart';
import 'package:gls_self_order/general/controllers/general_controller.dart';
import 'package:gls_self_order/general/controllers/news_controller.dart';
import 'package:gls_self_order/general/controllers/order_controller.dart';
import 'package:gls_self_order/general/controllers/payment_controller.dart';
import 'package:gls_self_order/general/controllers/product_controller.dart';
import 'package:gls_self_order/general/controllers/receipt_controller.dart';
import 'package:gls_self_order/general/views/auth/login_view.dart';
import 'package:gls_self_order/self_order/data/models/e_menu/menu_tree_model.dart';
import 'package:gls_self_order/self_order/presentation/e_menu/cart/cart_controller.dart';
import 'package:gls_self_order/self_order/routes/app_pages.dart';
import 'package:gls_self_order/sme/routes/sme_app_pages.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'core/controllers/language_controller.dart';
import 'core/lang/translation_service.dart';
import 'core/mapper/inactivity_wrapper.dart';
import 'core/network/dio_client.dart';
import 'core/theme/app_theme.dart';
import 'firebase_options.dart';
import 'general/classes/user_info.dart';
import 'general/controllers/revenue_controller.dart';
import 'general/views/report/home/<USER>/home.dart';
import 'general/views/report/service_locator.dart';
import 'sme/data/models/e_menu/menu_tree_sme_model.dart';
import 'sme/presentation/e_menu/cart/cart_controller.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  // SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  // SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  await Hive.initFlutter();
  await UserInfo.initDeviceId();
  setupServiceLocator();
  clearOnExit();

  Hive.registerAdapter(MenuTreeModelAdapter());
  Hive.registerAdapter(MenuTreeSMEModelAdapter());
  final sharedPreferences = await SharedPreferences.getInstance();

  Get.put(sharedPreferences);
  Get.put(DioClient());
  Get.put(DioClientSME());
  Get.put(LanguageController());
  Get.put(CartController());
  Get.put(CartSMEController());

  Get.put(BluetoothPrinterController());
  Get.put(UsbPrinterController());

  Get.put(GeneralController());
  Get.put(AuthController());
  Get.put(ProductController());
  Get.put(NewsController());
  Get.put(ReceiptController());
  Get.put(OrderController());
  Get.put(CustomerController());
  Get.put(RevenueController());
  Get.put(PaymentController());

  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final LanguageController languageController = Get.find();

    final bool isUsingHomeMainView = true;
    final isSmeMode = true;

    final pages = isSmeMode ? SmeAppPages.pages : AppPages.pages;
    final initialRoute = isUsingHomeMainView
        ? '/'
        : (isSmeMode ? '/splash-sme-page' : '/splash-page');

    Widget app = GetMaterialApp(
      debugShowCheckedModeBanner: false,
      theme: AppTheme.light,
      initialRoute: initialRoute,
      getPages: pages,
      locale: languageController.currentLocale.value,
      fallbackLocale: TranslationService.fallbackLocale,
      translations: TranslationService(),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      builder: (context, child) {
        return GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () => FocusScope.of(context).unfocus(),
          child: BotToastInit()(context, child),
        );
      },
      navigatorObservers: [BotToastNavigatorObserver()],
      home: isUsingHomeMainView ? const LoginView() : null,
    );

    return isUsingHomeMainView ? app : InactivityWrapper(child: app);
  }
}
