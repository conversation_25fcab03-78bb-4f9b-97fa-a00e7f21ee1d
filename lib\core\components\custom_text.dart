import 'package:flutter/material.dart';
class CustomText extends StatelessWidget {
  final String text;
  final bool bold;
  final bool italic;
  final double size;
  final Color color;
  final int? maxLines;
  final TextAlign? textAlign;

  const CustomText({
    super.key,
    required this.text,
    this.bold = false,
    this.italic = false,
    this.size = 16,
    this.color = Colors.black,
    this.maxLines = 1, // mặc định 1 dòng
    this.textAlign,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      maxLines: maxLines == 0 ? null : maxLines, // 0 nghĩa là không giới hạn dòng
      overflow: maxLines == 1 ? TextOverflow.ellipsis : TextOverflow.ellipsis,
      softWrap: true, // Cho phép text tự động xuống dòng
      textAlign: textAlign,
      style: TextStyle(
        color: color,
        fontSize: size,
        fontWeight: bold ? FontWeight.bold : FontWeight.normal,
        fontStyle: italic ? FontStyle.italic : FontStyle.normal,
        fontFamily: 'Roboto',
      ),
    );
  }
}
