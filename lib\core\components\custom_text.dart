import 'package:flutter/material.dart';

class CustomText extends StatelessWidget {
  final String text;
  final bool bold;
  final bool italic;
  final double size;
  final Color color;
  final int maxLines;
  const CustomText(
      {super.key,
      required this.text,
      this.bold = false,
      this.italic = false,
      this.size = 16,
      this.color = Colors.black,
      this.maxLines = 1});

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      maxLines: maxLines != 1 ? maxLines : null,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: color,
        fontSize: size,
        fontWeight: bold ? FontWeight.bold : FontWeight.normal,
        fontStyle: italic ? FontStyle.italic : FontStyle.normal,
        fontFamily: 'Roboto',
      ),
    );
  }
}
