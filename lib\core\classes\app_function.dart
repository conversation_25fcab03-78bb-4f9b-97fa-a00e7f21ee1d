import 'dart:math';

import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:intl/intl.dart';

import '../theme/app_style.dart';

class AppFunction {
  static String getPlatformStyle(context) {
    //pending logic
    return 'mobile';
  }

  static showSuccess(message){
    BotToast.showText(
        text: message.toString(),
        contentColor: AppColors.button,
        align: const Alignment(0, -0.8),
        textStyle: const TextStyle(fontSize: 16, color: Colors.white),
        clickClose: true
    );
  }

  static showError(message){
    BotToast.showText(
        text: message.toString(),
        contentColor: AppColors.danger,
        align: const Alignment(0, -0.8),
        textStyle: const TextStyle(fontSize: 16, color: Colors.white),
        clickClose: true
    );
  }

  static showLoading() {
    BotToast.showLoading();
  }

  static hideLoading() {
    BotToast.closeAllLoading();
  }

  static String generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final rand = Random();
    return List.generate(length, (index) => chars[rand.nextInt(chars.length)]).join();
  }

  static String formatDateNoTime(value) {
    return DateFormat('dd/MM/yyyy').format(DateTime.parse(value.toString()));
  }

  static String formatDateWithTime(value) {
    return DateFormat('dd/MM/yyyy HH:mm').format(DateTime.parse(value.toString()));
  }
  
  static String formatMoney(value) {
    return NumberFormat.currency(locale: 'vi_VN', symbol: '', decimalDigits: 0).format(num.parse(value.toString()));
  }

  static Widget getRenderPaymentMethod(payment) {
    if (payment == 'ZaloPay') {
      return Row(
        children: [
          CustomText(text: ' - '),
          Image.asset('assets/images/self_order/zalopay.png', width: 20,),
          CustomText(text: 'ZaloPay')
        ],
      );
    }
    if (payment == 'VNPay') {
      return Row(
        children: [
          CustomText(text: ' - '),
          Image.asset('assets/images/self_order/vnpay.png', width: 20,),
          CustomText(text: 'VNPay')
        ],
      );
    }
    if (payment == 'ACB') {
      return Row(
        children: [
          CustomText(text: ' - '),
          Image.asset('assets/images/self_order/acb.png', width: 20,),
          CustomText(text: 'ACB')
        ],
      );
    }
    return Row(
      children: [
        CustomText(text: ' - '),
        CustomText(text: 'Tiền mặt')
      ],
    );
  }

  static String getPaymentMethodName(code) {
    String text = '';
    switch (code) {
      case 'CASH':
        text = 'Tiền mặt';
        break;
      case 'ACB':
        text = 'ACB';
        break;
    }
    return text;
  }

  static Future<bool> showConfirmDialog(String title, String message) async {
    final width = Get.width;
    final result = await Get.dialog<bool>(
      Dialog(
        backgroundColor: AppColors.background,
        insetPadding: EdgeInsets.symmetric(
          horizontal: width * 0.05,
          vertical: width * 0.1,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(width * 0.02),
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: Get.height * 0.4,
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: width * 0.03),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: width * 0.02),
                        child: Text(
                          title,
                          style: PrimaryFont.bold.copyWith(
                            color: AppColors.text,
                            fontSize: width * 0.04,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: Icon(Icons.close,
                            color: AppColors.text, size: width * 0.05),
                        onPressed: () => Get.back(result: false),
                      ),
                    ],
                  ),
                  SizedBox(height: width * 0.02),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: width * 0.02),
                    child: Text(
                      message,
                      style: TextStyle(fontSize: width * 0.035),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(height: width * 0.03),
                  Padding(
                    padding: EdgeInsets.only(
                      bottom: width * 0.03,
                      right: width * 0.02,
                      left: width * 0.02,
                      top: width * 0.02,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        ElevatedButton(
                          onPressed: () => Get.back(result: false),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            minimumSize: Size(width * 0.2, width * 0.08),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(width * 0.02),
                            ),
                          ),
                          child: Text(
                            'Hủy',
                            style: PrimaryFont.bold.copyWith(
                              color: AppColors.background,
                              fontSize: width * 0.03,
                            ),
                          ),
                        ),
                        SizedBox(width: width * 0.02),
                        ElevatedButton(
                          onPressed: () => Get.back(result: true),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            minimumSize: Size(width * 0.2, width * 0.08),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(width * 0.02),
                            ),
                          ),
                          child: Text(
                            'Đồng ý',
                            style: PrimaryFont.bold.copyWith(
                              color: AppColors.background,
                              fontSize: width * 0.03,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );

    return result ?? false;
  }
}
