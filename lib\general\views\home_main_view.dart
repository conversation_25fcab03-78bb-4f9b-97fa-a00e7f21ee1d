import 'dart:io';

import 'package:flutter/material.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/core/theme/app_images.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/general/views/account/account_view.dart';
import 'package:gls_self_order/general/views/app_view.dart';
import 'package:gls_self_order/general/views/home_view.dart';
import 'package:gls_self_order/general/views/marketplace_view.dart';
import 'package:gls_self_order/general/views/notify_view.dart';
import 'package:gls_self_order/general/views/order/order_view.dart';
import 'package:gls_self_order/general/views/payment_view.dart';
import 'package:gls_self_order/general/views/test_printer_view.dart';

class HomeMainView extends StatefulWidget {
  const HomeMainView({super.key});

  @override
  State<HomeMainView> createState() => _HomeMainViewState();
}

class _HomeMainViewState extends State<HomeMainView> {
  //variable
  final PageStorageBucket bucket = PageStorageBucket();
  Widget currentView = const HomeView();
  List menuItems = [
    {'title': 'Trang chủ', 'icon': Icons.home_outlined, 'view': HomeView()},
    {'title': 'Đơn hàng', 'icon': Icons.shopping_bag_outlined, 'view': OrdersView()},
    {'title': 'Thanh toán', 'icon': Icons.payment, 'view': PaymentView()},
    {'title': 'Thông báo', 'icon': Icons.notifications_none, 'view': NotifyView()},
    {'title': 'Cài đặt', 'icon': Icons.settings, 'view': AccountView(from: 'bottom',)},
    // {'title': 'Marketplace', 'icon': Icons.shopping_cart, 'view': MarketplaceView()},
  ];
  int active = 0;

  //function
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  Widget renderMenu() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int index = 0; index < menuItems.length; index++)
          InkWell(
            onTap: () {
              currentView = menuItems[index]['view'];
              active = index;
              setState(() {

              });
            },
            child: Padding(
              padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Icon(menuItems[index]['icon'], color: active == index ? AppColors.primary : AppColors.shadow),
                  Text(menuItems[index]['title'], style: TextStyle(color: active == index ? AppColors.primary : AppColors.shadow, fontSize: 13))
                ],
              ),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          return;
        }

        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: CustomText(text: 'Xác nhận', size: 20,),
              content: CustomText(text: 'Bạn có chắc muốn thoát ứng dụng?'),
              actions: <Widget>[
                TextButton(
                  onPressed: () {
                    Get.back();
                  },
                  child: CustomText(text: 'Ở lại'),
                ),
                TextButton(
                  onPressed: () {
                    exit(0);
                  },
                  child: CustomText(text: 'Thoát'),
                ),
              ],
            );
          },
        );
      },
      child: Scaffold(
        body: PageStorage(
          bucket: bucket,
          child: currentView,
        ),
        bottomNavigationBar: BottomAppBar(
          color: Colors.white,
          shadowColor: Colors.grey,
          padding: EdgeInsets.fromLTRB(2, 0, 2, 0),
          child: renderMenu(),
        ),
      ),
    );
  }
}
