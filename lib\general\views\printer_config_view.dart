import 'package:flutter/material.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/controllers/bluetooth_printer_controller.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:get/get.dart';

import '../../core/components/custom_button.dart';

class PrinterConfigView extends StatefulWidget {
  const PrinterConfigView({super.key});

  @override
  State<PrinterConfigView> createState() => _PrinterConfigViewState();
}

class _PrinterConfigViewState extends State<PrinterConfigView> {
  //variable
  BluetoothPrinterController printerController = Get.find();

  //function
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: '<PERSON><PERSON><PERSON> hình m<PERSON>y in'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        automaticallyImplyLeading: false,
        // leading: CustomBackButton(),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: Obx(() {
          return ListView(
            padding: EdgeInsets.all(5),
            children: [
              CustomButton(text: 'Connect', onTap: () {
                printerController.isConnected.value = true;
              }, color: AppColors.primary),
              for(int index = 0; index < printerController.devices.length; index++)
                InkWell(
                  onTap: () {
                    printerController.connectPrinter(printerController.devices[index]['mac']);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(10)
                    ),
                    padding: EdgeInsets.all(10),
                    margin: EdgeInsets.fromLTRB(0, 0, 0, 5),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(text: printerController.devices[index]['name'], color: Colors.white,),
                        CustomText(text: printerController.devices[index]['mac'], color: Colors.white,),
                      ],
                    ),
                  ),
                )
            ],
          );
        }),
      ),
    );
  }
}
