import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import '../../../../core/classes/app_function.dart';
import '../../../../core/theme/app_style.dart';
import '../../../controllers/bank_account_controller.dart';
import 'terms_pdf_view.dart';

class BankAccountFormView extends StatelessWidget {
  final BankAccountController controller;

  BankAccountFormView({super.key}) : controller = Get.put(BankAccountController()) {
    // Reset form và fetch dữ liệu mới khi widget được tạo
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.resetForm();
      controller.fetchExistingAccount();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        leading: const CustomBackButton(),
        title: const CustomText(
          text: "Thông tin tài khoản",
          size: 20,
          bold: true,
          color: Colors.white,
        ),
        centerTitle: true,
      ),
      body: GetBuilder<BankAccountController>(
        builder: (controller) {
          if (controller.isLoading && !controller.hasExistingAccount) {
            return const Center(child: CircularProgressIndicator());
          }

          return Stack(
            children: [
              SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (controller.hasExistingAccount)
                      Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFFE8F5E9),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.all(12),
                        child: Row(
                          children: [
                            const Icon(Icons.check_circle, color: Colors.green),
                            const SizedBox(width: 8),
                            const Expanded(
                              child: CustomText(
                                text: "Tài khoản ACB đã được liên kết với chi nhánh này",
                                size: 14,
                              ),
                            ),
                          ],
                        ),
                      )
                    else
                      Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFFE0F3FF),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.all(12),
                        child: Row(
                          children: [
                            const Icon(Icons.info, color: Colors.blue),
                            const SizedBox(width: 8),
                            const Expanded(
                              child: CustomText(
                                text: "Nhập thông tin tài khoản ACB của bạn",
                                size: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    const SizedBox(height: 16),

                    // Bank name (fixed as ACB)
                    const CustomText(text: "Tên ngân hàng"),
                    const SizedBox(height: 4),
                    const CustomText(text: "Ngân hàng thương mại cổ phần á châu (ACB)", bold: true),
                    const SizedBox(height: 16),

                    // Account number
                    _buildRequiredField(
                      label: "Số tài khoản",
                      hintText: "Nhập số tài khoản ACB",
                      initialValue: controller.accountNumber,
                      onChanged: (value) => controller.accountNumber = value,
                      keyboardType: TextInputType.number,
                      enabled: !controller.hasExistingAccount,
                    ),
                    const SizedBox(height: 16),
                    // Phone number
                    _buildRequiredField(
                      label: "Số điện thoại",
                      hintText: "Nhập số điện thoại liên kết",
                      initialValue: controller.phoneNumber,
                      onChanged: (value) => controller.phoneNumber = value,
                      keyboardType: TextInputType.phone,
                      enabled: !controller.hasExistingAccount,
                    ),

                    // Customer type
                    _buildSectionTitle("Loại khách hàng"),
                    Row(
                      children: [
                        Radio<String>(
                          value: 'PERS',
                          groupValue: controller.customerType,
                          onChanged: controller.hasExistingAccount
                              ? null
                              : (value) {
                            controller.customerType = value!;
                            if (value == 'PERS') {
                              controller.pastDayNotification = 'NONE';
                            }
                            controller.update();
                          },
                        ),
                        const CustomText(text: "Cá nhân"),
                        const SizedBox(width: 20),
                        Radio<String>(
                          value: 'ORG',
                          groupValue: controller.customerType,
                          onChanged: controller.hasExistingAccount
                              ? null
                              : (value) {
                            controller.customerType = value!;
                            controller.update();
                          },
                        ),
                        const CustomText(text: "Doanh nghiệp"),
                      ],
                    ),
                    if (controller.customerType == 'ORG')
                      _buildField(
                        label: "Tên đăng nhập",
                        hintText: "Nhập tên đăng nhập",
                        initialValue: controller.username,
                        onChanged: (value) => controller.username = value,
                        keyboardType: TextInputType.text,
                        enabled: !controller.hasExistingAccount,
                      ),
                    const SizedBox(height: 16),
                    _buildVirtualAccountField(context),

                    // Real-time notifications
                    // _buildSectionTitle("Thông báo thời gian thực"),
                    // _buildNotificationOption(
                    //   value: 'ALL',
                    //   groupValue: controller.realTimeNotification,
                    //   label: "Tất cả thông báo",
                    //   onChanged: controller.hasExistingAccount
                    //       ? null
                    //       : (value) {
                    //     controller.realTimeNotification = value!;
                    //     controller.update();
                    //   },
                    // ),
                    // _buildNotificationOption(
                    //   value: 'CREDIT',
                    //   groupValue: controller.realTimeNotification,
                    //   label: "Chỉ thông báo nhận tiền",
                    //   onChanged: controller.hasExistingAccount
                    //       ? null
                    //       : (value) {
                    //     controller.realTimeNotification = value!;
                    //     controller.update();
                    //   },
                    // ),
                    // _buildNotificationOption(
                    //   value: 'DEBIT',
                    //   groupValue: controller.realTimeNotification,
                    //   label: "Chỉ thông báo trừ tiền",
                    //   onChanged: controller.hasExistingAccount
                    //       ? null
                    //       : (value) {
                    //     controller.realTimeNotification = value!;
                    //     controller.update();
                    //   },
                    // ),
                    // _buildNotificationOption(
                    //   value: 'NONE',
                    //   groupValue: controller.realTimeNotification,
                    //   label: "Không thông báo",
                    //   onChanged: controller.hasExistingAccount
                    //       ? null
                    //       : (value) {
                    //     controller.realTimeNotification = value!;
                    //     controller.update();
                    //   },
                    // ),

                    // Past day notifications (only for ORG)
                    // if (controller.customerType == 'ORG') ...[
                    //   _buildSectionTitle("Thông báo qua ngày"),
                    //   _buildNotificationOption(
                    //     value: 'ALL',
                    //     groupValue: controller.pastDayNotification, // <-- Sửa thành pastDayNotification
                    //     label: "Tất cả thông báo",
                    //     onChanged: controller.hasExistingAccount
                    //         ? null
                    //         : (value) {
                    //       controller.pastDayNotification = value!;
                    //       controller.update();
                    //     },
                    //   ),
                    //   _buildNotificationOption(
                    //     value: 'CREDIT',
                    //     groupValue: controller.pastDayNotification, // <-- Sửa thành pastDayNotification
                    //     label: "Chỉ thông báo nhận tiền",
                    //     onChanged: controller.hasExistingAccount
                    //         ? null
                    //         : (value) {
                    //       controller.pastDayNotification = value!;
                    //       controller.update();
                    //     },
                    //   ),
                    //   _buildNotificationOption(
                    //     value: 'DEBIT',
                    //     groupValue: controller.pastDayNotification, // <-- Sửa thành pastDayNotification
                    //     label: "Chỉ thông báo trừ tiền",
                    //     onChanged: controller.hasExistingAccount
                    //         ? null
                    //         : (value) {
                    //       controller.pastDayNotification = value!;
                    //       controller.update();
                    //     },
                    //   ),
                    //   _buildNotificationOption(
                    //     value: 'NONE',
                    //     groupValue: controller.pastDayNotification, // <-- Sửa thành pastDayNotification
                    //     label: "Không thông báo",
                    //     onChanged: controller.hasExistingAccount
                    //         ? null
                    //         : (value) {
                    //       controller.pastDayNotification = value!;
                    //       controller.update();
                    //     },
                    //   ),
                    // ],

                    _buildTermsCheckbox(),
                    const SizedBox(height: 16),
                    _buildAccountUsageOptions(),
                    if (controller.hasExistingAccount) // Chỉ hiển thị khi có tài khoản liên kết
                      SizedBox(
                        width: double.infinity,
                        height: 45,
                        child: TextButton(
                          onPressed: controller.isLoading
                              ? null
                              : () async {
                            final confirm = await AppFunction.showConfirmDialog(
                              'Xác nhận hủy',
                              'Bạn có chắc chắn muốn hủy liên kết tài khoản ACB này?',
                            );
                            if (confirm == true) {
                              await controller.requestCancelAccount();
                            }
                          },
                          style: TextButton.styleFrom(
                            foregroundColor: AppColors.danger,
                            padding: EdgeInsets.zero,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            textStyle: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          child: const Text("Hủy liên kết tài khoản"),
                        ),
                      ),
                    const SizedBox(height: 80),
                  ],
                ),
              ),

              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  color: Colors.white,
                  child: Column(
                    children: [
                      // SizedBox(
                      //   width: double.infinity,
                      //   height: 45,
                      //   child: OutlinedButton(
                      //     onPressed: () {
                      //       controller.printInputData();
                      //       print("Đã in thông tin ra console!");
                      //     },
                      //     style: OutlinedButton.styleFrom(
                      //       side: const BorderSide(color: AppColors.primary),
                      //       shape: RoundedRectangleBorder(
                      //         borderRadius: BorderRadius.circular(6),
                      //       ),
                      //     ),
                      //     child: const CustomText(
                      //       text: "Kiểm tra thông tin (Print Console)",
                      //       color: AppColors.primary,
                      //       bold: true,
                      //     ),
                      //   ),
                      // ),
                      // const SizedBox(height: 10),
                      SizedBox(
                        width: double.infinity,
                        height: 45,
                        child: ElevatedButton(
                          onPressed: controller.isLoading || controller.hasExistingAccount || !controller.acceptedTerms
                              ? null
                              : () async {
                            await controller.registerBankAccount();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                          ),
                          child: controller.isLoading
                              ? const CircularProgressIndicator(color: Colors.white)
                              : Text(
                            controller.hasExistingAccount
                                ? "Tài khoản đã được liên kết"
                                : "Kết nối tài khoản",
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),

                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 16, bottom: 8),
      child: CustomText(
        text: title,
        size: 16,
        bold: true,
      ),
    );
  }

  Widget _buildRequiredField({
    required String label,
    required String hintText,
    required Function(String) onChanged,
    String? initialValue,
    bool enabled = true,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CustomText(text: label, size: 16),
            CustomText(text: '*', color: AppColors.danger, size: 16),
          ],
        ),
        const SizedBox(height: 4),
        TextFormField(
          initialValue: initialValue,
          onChanged: onChanged,
          keyboardType: keyboardType,
          enabled: enabled,
          decoration: InputDecoration(
            hintText: hintText,
            border: const UnderlineInputBorder(),
          ),
        ),
      ],
    );
  }

  Widget _buildField({
    required String label,
    required String hintText,
    required Function(String) onChanged,
    String? initialValue,
    bool enabled = true,
    TextInputType keyboardType = TextInputType.text,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(text: label, size: 16),
        const SizedBox(height: 4),
        TextFormField(
          initialValue: initialValue,
          onChanged: onChanged,
          keyboardType: keyboardType,
          enabled: enabled,
          decoration: InputDecoration(
            hintText: hintText,
            border: const UnderlineInputBorder(),
          ),
        ),
      ],
    );
  }

  // Widget _buildNotificationOption({
  //   required String value,
  //   required String groupValue,
  //   required String label,
  //   required Function(String?)? onChanged,
  // }) {
  //   return RadioListTile<String>(
  //     value: value,
  //     groupValue: groupValue,
  //     onChanged: onChanged,
  //     title: CustomText(text: label),
  //     contentPadding: EdgeInsets.zero,
  //     dense: true,
  //   );
  // }

  Widget _buildTermsCheckbox() {
    return AnimatedCrossFade(
      duration: const Duration(milliseconds: 100),
      crossFadeState: controller.hasExistingAccount
          ? CrossFadeState.showSecond
          : CrossFadeState.showFirst,
      firstChild: Row(
        children: [
          Checkbox(
            value: controller.acceptedTerms,
            onChanged: (value) {
              controller.acceptedTerms = value ?? false;
              controller.update();
            },
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => Get.to(() => const TermsPdfView()),
              child: RichText(
                text: const TextSpan(
                  style: TextStyle(fontSize: 14, color: Colors.black),
                  children: [
                    TextSpan(text: "Tôi đã xác nhận đã đọc và đồng ý với "),
                    TextSpan(
                      text: "điều khoản và điều kiện dịch vụ",
                      style: TextStyle(
                        color: Colors.blue,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                    TextSpan(text: " của Ngân hàng ACB"),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      secondChild: const SizedBox.shrink(),
    );
  }

  Widget _buildVirtualAccountField(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const CustomText(text: "Tài khoản LOCPHAT (nếu có)", size: 16),
        const SizedBox(height: 4),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: controller.virtualAccountController,
                enabled: !controller.hasExistingAccount,
                keyboardType: TextInputType.text,
                decoration: InputDecoration(
                  hintText: "Nhập số tài khoản LOCPHAT",
                  border: const UnderlineInputBorder(),
                ),
              ),
            ),
            if (controller.hasExistingAccount)
              TextButton(
                onPressed: () => _showUpdateVirtualAccountDialog(context),
                child: const Text(
                  "Cập nhật",
                  style: TextStyle(color: AppColors.primary),
                ),
              ),
          ],
        ),
      ],
    );
  }

  void _showUpdateVirtualAccountDialog(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final RxString errorText = ''.obs;

    Get.dialog(
      Dialog(
        backgroundColor: AppColors.background,
        insetPadding: EdgeInsets.symmetric(
          horizontal: width * 0.05,
          vertical: width * 0.1,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(width * 0.02),
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.5,
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: width * 0.03),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: width * 0.02),
                        child: Text(
                          'Cập nhật tài khoản LOCPHAT',
                          style: PrimaryFont.bold.copyWith(
                            color: AppColors.text,
                            fontSize: width * 0.04,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: Icon(Icons.close,
                            color: AppColors.text, size: width * 0.05),
                        onPressed: () => Get.back(),
                      ),
                    ],
                  ),
                  SizedBox(height: width * 0.01),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: width * 0.02),
                    child: Obx(() => TextField(
                      controller: controller.virtualAccountController,
                      keyboardType: TextInputType.text,
                      decoration: InputDecoration(
                        hintText: 'Nhập số tài khoản LOCPHAT',
                        hintStyle: PrimaryFont.regular.copyWith(
                          color: AppColors.shadow,
                          fontSize: width * 0.03,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(width * 0.01),
                        ),
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: width * 0.03,
                          vertical: width * 0.03,
                        ),
                        errorText:
                        errorText.value.isEmpty ? null : errorText.value,
                      ),
                      style: PrimaryFont.regular.copyWith(
                        color: AppColors.text,
                        fontSize: width * 0.03,
                      ),
                      onChanged: (_) => errorText.value = '',
                    )),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      bottom: width * 0.03,
                      right: width * 0.02,
                      left: width * 0.02,
                      top: width * 0.02,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        ElevatedButton(
                          onPressed: () => Get.back(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            minimumSize: Size(width * 0.2, width * 0.08),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(width * 0.02),
                            ),
                          ),
                          child: Text(
                            'Hủy',
                            style: PrimaryFont.bold.copyWith(
                              color: AppColors.background,
                              fontSize: width * 0.03,
                            ),
                          ),
                        ),
                        SizedBox(width: width * 0.02),
                        ElevatedButton(
                          onPressed: () async {
                            // if (controller.virtualAccountController.text.trim().isEmpty) {
                            //   errorText.value = 'Vui lòng nhập số tài khoản';
                            //   return;
                            // }
                            Get.back();
                            await controller.updateVirtualAccount();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            minimumSize: Size(width * 0.2, width * 0.08),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(width * 0.02),
                            ),
                          ),
                          child: Text(
                            'Xác nhận',
                            style: PrimaryFont.bold.copyWith(
                              color: AppColors.background,
                              fontSize: width * 0.03,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  Widget _buildAccountUsageOptions() {
    return AnimatedCrossFade(
      duration: const Duration(milliseconds: 100),
      crossFadeState: controller.hasExistingAccount
          ? CrossFadeState.showFirst
          : CrossFadeState.showSecond,
      firstChild:Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle("Sử dụng tài khoản"),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () async {
                      if (controller.hasExistingAccount &&
                          !controller.useMainAccount) {
                        controller.useMainAccount = true;
                        controller.useVirtualAccount = false;
                        controller.update();
                        final success = await controller.updateAccountUsage();
                        if (success) {
                          AppFunction.showSuccess('Đã chuyển sang sử dụng tài khoản chính');
                        }
                      }
                    },
                    style: OutlinedButton.styleFrom(
                      backgroundColor: controller.useMainAccount
                          ? AppColors.primary.withOpacity(0.1)
                          : Colors.transparent,
                      side: BorderSide(
                        color: controller.useMainAccount
                            ? AppColors.primary
                            : Colors.grey,
                      ),
                    ),
                    child: Text(
                      "Tài khoản chính",
                      style: TextStyle(
                        color: controller.useMainAccount
                            ? AppColors.primary
                            : Colors.grey,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () async {
                      if (controller.hasExistingAccount &&
                          controller.virtualAccountNumber.isNotEmpty &&
                          !controller.useVirtualAccount) {
                        controller.useMainAccount = false;
                        controller.useVirtualAccount = true;
                        controller.update();
                        final success = await controller.updateAccountUsage();
                        if (success) {
                          AppFunction.showSuccess('Đã chuyển sang sử dụng tài khoản LOCPHAT');
                        }
                      }
                    },
                    style: OutlinedButton.styleFrom(
                      backgroundColor: controller.useVirtualAccount
                          ? AppColors.primary.withOpacity(0.1)
                          : Colors.transparent,
                      side: BorderSide(
                        color: controller.virtualAccountNumber.isEmpty
                            ? Colors.grey
                            : controller.useVirtualAccount
                            ? AppColors.primary
                            : Colors.grey,
                      ),
                    ),
                    child: Text(
                      "LOCPHAT",
                      style: TextStyle(
                        color: controller.virtualAccountNumber.isEmpty
                            ? Colors.grey
                            : controller.useVirtualAccount
                            ? AppColors.primary
                            : Colors.grey,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      secondChild: const SizedBox.shrink(),
    );
  }
}