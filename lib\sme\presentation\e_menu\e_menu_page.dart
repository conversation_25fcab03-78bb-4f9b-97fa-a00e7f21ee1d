import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:intl/intl.dart';
import 'package:simple_barcode_scanner/simple_barcode_scanner.dart';
import '../../../core/components/custom_text.dart';
import '../../routes/sme_app_routes.dart';
import 'cart/cart_controller.dart';
import 'e_menu_binding.dart';
import 'e_menu_controller.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_style.dart';
import '../../../core/widgets/default_button.dart';
import 'widgets/category_widget.dart';
import 'widgets/product_widget.dart';
import 'widgets/search_widget.dart';
import 'widgets/shimmer_loading.dart';

class EMenuPage extends StatelessWidget {
  final EMenuController controller = Get.find<EMenuController>();

  EMenuPage({super.key});

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '', decimalDigits: 0);
    EMenuBinding().dependencies();

    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Obx(() {
          if (controller.isLoading.value) {
            return shimmerLoading(width);
          }

          if (controller.error.isNotEmpty) {
            return Center(child: Text('Lỗi: ${controller.error.value}'));
          }

          if (controller.allCategories.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomText(text: 'Vui lòng tạo sản phẩm trong danh mục', bold: true,),
                  SizedBox(height: width * 0.03),
                  ElevatedButton.icon(
                    onPressed: () {
                      Get.back();
                    },
                    label: const Text('Quay lại'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.background,
                      padding: EdgeInsets.symmetric(
                        horizontal: width * 0.05,
                        vertical: width * 0.025,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }

          return Padding(
            padding: EdgeInsets.symmetric(horizontal: width * 0.015, vertical: width * 0.015),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        padding: EdgeInsets.all(width * 0.015),
                        decoration: BoxDecoration(
                          color: AppColors.background,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.arrow_back_ios_new_rounded,
                          size: width * 0.045,
                          color: AppColors.text,
                        ),
                      ),
                    ),
                    SizedBox(width: width * 0.03),
                    Expanded(child: searchBar(width, false)),
                    SizedBox(width: width * 0.03),
                    GestureDetector(
                      onTap: () async {
                        String? res = await SimpleBarcodeScanner.scanBarcode(
                          context,
                          barcodeAppBar: const BarcodeAppBar(
                            appBarTitle: 'Test',
                            centerTitle: false,
                            enableBackButton: true,
                            backButtonIcon: Icon(Icons.arrow_back_ios),
                          ),
                          isShowFlashIcon: true,
                          delayMillis: 500,
                          cameraFace: CameraFace.back,
                          scanFormat: ScanFormat.ONLY_BARCODE,
                        );
                        String code = res as String;
                        dynamic prod;
                        for(dynamic item in controller.allProducts) {
                          if (item['Barcode'] == code) {
                            prod = item;
                            break;
                          }
                        }
                        if (prod != null) {
                          Get.toNamed(SmeAppRoutes.productDetail, arguments: prod);
                        }
                        else {
                          AppFunction.showError('Không tìm thấy sản phẩm');
                        }
                      },
                      child: Container(
                        padding: EdgeInsets.all(width * 0.03),
                        decoration: BoxDecoration(
                          color: AppColors.background,
                          borderRadius: BorderRadius.circular(10),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.document_scanner_outlined,
                          size: width * 0.05,
                          color: AppColors.text,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: width * 0.015),
                // Category
                SizedBox(
                  height: width * 0.225,
                  child: Obx(() {
                    final validCategories = controller.allCategories
                        .where((cat) => controller.hasProducts(cat))
                        .toList();

                    return CategoryWidget(
                      width: width,
                      itemGroups: validCategories,
                      selectedIndex: controller.selectedCategoryIndex.value,
                      onSelected: (index) => controller.selectedCategoryIndex.value = index,
                    );
                  }),
                ),
                // Product grid
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: width * 0.015),
                    child: Obx(() {
                      return CustomScrollView(
                        slivers: [
                          if (controller.getChildrenCategories().isNotEmpty)
                            ...controller.getGroupedProductsByChildCategory().entries.map((entry) {
                              final title = entry.key;
                              final productList = entry.value;

                              return SliverList(
                                delegate: SliverChildListDelegate([
                                  Text(
                                    title,
                                    style: PrimaryFont.bold.copyWith(
                                      fontSize: width * 0.03,
                                      color: AppColors.text,
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsets.symmetric(vertical: width * 0.015),
                                    child: GridView.builder(
                                      shrinkWrap: true,
                                      physics: const NeverScrollableScrollPhysics(),
                                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                        crossAxisCount: 3,
                                        mainAxisSpacing: width * 0.03,
                                        crossAxisSpacing: width * 0.03,
                                        childAspectRatio: 0.75,
                                      ),
                                      itemCount: productList.length,
                                      itemBuilder: (_, index) {
                                        final product = productList[index];
                                        final itemName = product['ItemName'] ?? '';
                                        final price = (product['Price'] as num?)?.toDouble() ?? 0.0;
                                        final images = product['Images'] as List? ?? [];
                                        final imageUrl = images.isNotEmpty ? images.first['MediaUrl'] ?? '' : '';

                                        return productItem(
                                          width,
                                          currencyFormat,
                                          itemName,
                                          price,
                                          imageUrl,
                                          product,
                                        );
                                      },
                                    ),
                                  ),
                                ]),
                              );
                            })
                          else
                            SliverGrid(
                              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 3,
                                mainAxisSpacing: width * 0.03,
                                crossAxisSpacing: width * 0.03,
                                childAspectRatio: 0.75,
                              ),
                              delegate: SliverChildBuilderDelegate(
                                    (_, index) {
                                  final product = controller.getProductsForSelectedCategory()[index];
                                  final itemName = product['ItemName'] ?? '';
                                  final price = (product['Price'] as num?)?.toDouble() ?? 0.0;
                                  final images = product['Images'] as List? ?? [];
                                  final imageUrl = images.isNotEmpty ? images.first['MediaUrl'] ?? '' : '';

                                  return productItem(
                                    width,
                                    currencyFormat,
                                    itemName,
                                    price,
                                    imageUrl,
                                    product,
                                  );
                                },
                                childCount: controller.getProductsForSelectedCategory().length,
                              ),
                            ),
                        ],
                      );
                    }),
                  ),
                ),
              ],
            ),
          );
        }),
      ),
      bottomNavigationBar: Obx(() {
        if (controller.isLoading.value) {
          return shimmerLoading(width);
        }
        return buildBottomCartBar(width, currencyFormat);
      }),
    );
  }
}

Widget buildBottomCartBar(double width, NumberFormat currencyFormat) {
  final CartSMEController cartController = Get.find<CartSMEController>();

  return Obx(() {
    final itemCount = cartController.cartItems.length;
    final total = cartController.totalPrice.value;
    final totalQuantity = cartController.totalItemCount;

    return Padding(
      padding: EdgeInsets.only(left: width * 0.03, right: width * 0.03, bottom: width * 0.03),
      child: DefaultButton(
        onPress: () {
          if (itemCount > 0) {
            Get.toNamed(SmeAppRoutes.checkout);
          }
        },
        color: AppColors.primary,
        widthPercentage: 1,
        borderRadius: width * 0.02,
        heightPercentage: 0.12,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: width * 0.05),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Text(
                    'cart'.tr,
                    style: PrimaryFont.bold.copyWith(
                      color: AppColors.background,
                      fontSize: width * 0.04,
                    ),
                  ),
                  Text(
                    Intl.plural(
                      itemCount,
                      one: ' ($itemCount ${'item'.tr} - ${'qty'.tr}: $totalQuantity)',
                      other: ' ($itemCount ${'items'.tr} - ${'qty'.tr}: $totalQuantity)',
                      locale: Get.locale?.languageCode ?? 'en',
                    ),
                    style: PrimaryFont.bold.copyWith(
                      color: AppColors.background,
                      fontSize: width * 0.04,
                    ),
                  ),
                ],
              ),
              Text(
                currencyFormat.format(total),
                style: PrimaryFont.bold.copyWith(
                  color: AppColors.background,
                  fontSize: width * 0.04,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  });
}