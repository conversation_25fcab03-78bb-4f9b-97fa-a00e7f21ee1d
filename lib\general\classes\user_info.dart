import '../../core/classes/device_info.dart';

class UserInfo {
  static dynamic user;
  static List branches = [];

  static String username = '';
  static String password = '';
  static int branchId = 0;
  static String deviceId = '';
  static int orgId = 0;
  //variable for function
  static bool activeLicense = false;
  static bool activeACB = false;

  static Future<void> initDeviceId() async {
    deviceId = await DeviceInfo.getDeviceId();
  }

  static String licenseKey = '';
  static DateTime? startDate;
  static DateTime? expiryDate;
  static int daysLeft = 0;

}