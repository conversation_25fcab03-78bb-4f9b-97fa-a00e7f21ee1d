import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_multi_formatter/flutter_multi_formatter.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_money_field.dart';
import 'package:gls_self_order/core/components/custom_save_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/product_controller.dart';
import 'package:image_picker/image_picker.dart';
import 'package:simple_barcode_scanner/simple_barcode_scanner.dart';

class ProductCreateOrUpdateView extends StatefulWidget {
  final dynamic item;
  const ProductCreateOrUpdateView({super.key, this.item});

  @override
  State<ProductCreateOrUpdateView> createState() => _ProductCreateOrUpdateViewState();
}

class _ProductCreateOrUpdateViewState extends State<ProductCreateOrUpdateView> {
  //variable
  ProductController productController = Get.find();
  File? image;
  String imageOnline = '';
  final ImagePicker imagePicker = ImagePicker();
  List units = [];
  List categories = [];
  TextEditingController productNameController = TextEditingController();
  TextEditingController priceController = TextEditingController();
  TextEditingController originalPriceController = TextEditingController();
  TextEditingController categoryController = TextEditingController();
  TextEditingController vatPercentController = TextEditingController();
  TextEditingController unitController = TextEditingController();
  TextEditingController barCodeController = TextEditingController();
  int categorySelected = 0;
  int unitSelected = 0;
  bool includeVat = false;
  bool isActive = true;
  String availableStatus = 'AVAILABLE';
  String itemNo = '';

  //function
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getDetail();
    getUnits();
    getCategories();
  }

  getDetail() async {
    if(widget.item != null) {
      AppFunction.showLoading();
      dynamic item =  await productController.getDetailProduct(widget.item['Id']);
      if(item != null) {
        productNameController.text = item['ItemName'];
        priceController.text = toCurrencyString(
          item['Price'].toStringAsFixed(0),
          leadingSymbol: '',
          useSymbolPadding: true,
          thousandSeparator: ThousandSeparator.Period,
          mantissaLength: 0,
        );
        originalPriceController.text = toCurrencyString(
          item['CostPrice'].toStringAsFixed(0),
          leadingSymbol: '',
          useSymbolPadding: true,
          thousandSeparator: ThousandSeparator.Period,
          mantissaLength: 0,
        );
        vatPercentController.text = toCurrencyString(
          item['VatPercent'].toStringAsFixed(1),
          leadingSymbol: '',
          useSymbolPadding: true,
          thousandSeparator: ThousandSeparator.Period,
          mantissaLength: 1,
        );
        categorySelected = item['ItemGroupId'];
        unitSelected = item['UnitId'];
        includeVat = item['IncludeVat'];
        availableStatus = item['AvailableStatus'];
        isActive = item['IsActive'];
        barCodeController.text = item['Barcode'] ?? '';
        if (item['MediaUrls'].isNotEmpty) {
          imageOnline = item['MediaUrls'][item['MediaUrls'].length - 1]['MediaUrl'];
        }
        itemNo = item['ItemNo'] ?? '';
        AppFunction.hideLoading();
        setState(() {

        });
      }
    }
  }

  getCategories() async {
    categories = await productController.getCategories();
    if (widget.item != null) {
      for(int i = 0; i < categories.length; i++) {
        if (categories[i]['Id'] == widget.item['ItemGroupId']) {
          categoryController.text = categories[i]['ItemGroupName'];
        }
      }
    }
  }

  getUnits() async {
    units = await productController.getUnits();
    if (widget.item != null) {
      for(int i = 0; i < units.length; i++) {
        if (units[i]['UOM_AUTOID'] == widget.item['UnitId']) {
          unitController.text = units[i]['UOM_NAME'];
        }
      }
    }
  }

  Future<void> pickImageFromGallery() async {
    final XFile? pickedFile = await imagePicker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 85,
    );
    if (pickedFile != null) {
      setState(() => image = File(pickedFile.path));
    }
  }

  Future<void> pickImageFromCamera() async {
    final XFile? pickedFile = await imagePicker.pickImage(
      source: ImageSource.camera,
      imageQuality: 85,
    );

    if (pickedFile != null) {
      setState(() => image = File(pickedFile.path));
    }
  }

  pickCategory() {
    if (categories.isEmpty) return;

    _showSearchableBottomSheet(
      title: 'Chọn danh mục',
      titleHint: 'Tìm kiếm danh mục',
      items: categories,
      displayKey: 'ItemGroupName',
      selectedId: categorySelected,
      onItemSelected: (item) {
        setState(() {
          categorySelected = item['Id'];
          categoryController.text = item['ItemGroupName'];
        });
      },
    );
  }

  pickUnit() {
    if (units.isEmpty) return;

    _showSearchableBottomSheet(
      title: 'Chọn đơn vị tính',
      titleHint: 'Tìm kiếm đơn vị tính',
      items: units,
      displayKey: 'UOM_NAME',
      selectedId: unitSelected,
      onItemSelected: (item) {
        setState(() {
          unitSelected = item['UOM_AUTOID'];
          unitController.text = item['UOM_NAME'];
        });
      },
    );
  }

  save() async {
    AppFunction.showLoading();
    int productId = await productController.postCreateOrUpdateProduct(
        widget.item != null ? widget.item['Id'] : null,
        productNameController.text,
        toNumericString(originalPriceController.text),
        toNumericString(priceController.text),
        categorySelected,
        toNumericString(vatPercentController.text, allowPeriod: true, mantissaSeparator: ','),
        availableStatus,
        includeVat,
        unitSelected,
        unitController.text,
        barCodeController.text,
        isActive,
        itemNo
    );
    if (productId != 0) {
      if (image != null) {
        String imagePath = await productController.postUploadImageProduct(image);
        if (imagePath != '') {
          await productController.postAddImageForProduct(productId, imagePath);
        }
      }
      Get.back(result: productId);
    }
    AppFunction.hideLoading();
  }

  scanBarcode() async {
    try {
      String? res = await SimpleBarcodeScanner.scanBarcode(
        context,
        barcodeAppBar: const BarcodeAppBar(
          appBarTitle: 'Quét mã vạch',
          centerTitle: false,
          enableBackButton: true,
          backButtonIcon: Icon(Icons.arrow_back_ios),
        ),
        isShowFlashIcon: true,
        delayMillis: 500,
        cameraFace: CameraFace.back,
        scanFormat: ScanFormat.ONLY_BARCODE,
      );

      if (res != null && res.isNotEmpty && res != "-1") {
        setState(() {
          barCodeController.text = res;
        });
      } else {
        AppFunction.showError('Không thể quét mã hoặc mã không hợp lệ');
      }
    } catch (e) {
      AppFunction.showError('Lỗi khi quét mã: ${e.toString()}');
    }
  }

  void _showSearchableBottomSheet({
    required String title,
    required String titleHint,
    required List<dynamic> items,
    required String displayKey,
    required int selectedId,
    required Function(dynamic) onItemSelected,
  }) {
    final searchController = TextEditingController();
    List<dynamic> filteredItems = List.from(items);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return SizedBox(
              height: MediaQuery.of(context).size.height * 0.9,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(text: title, bold: true),
                        IconButton(
                          icon: Icon(Icons.close, color: AppColors.text, size: 20),
                          onPressed: () {
                            Get.back();
                          },
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: CustomTextField(
                      controller: searchController,
                      showLabel: false,
                      hint: titleHint,
                      prefixIcon: Icons.search,
                      onChanged: (value) {
                        setState(() {
                          filteredItems = items.where((item) {
                            return item[displayKey]
                                .toString()
                                .toLowerCase()
                                .contains(value.toLowerCase());
                          }).toList();
                        });
                      },
                    ),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: filteredItems.length,
                      itemBuilder: (context, index) {
                        final item = filteredItems[index];
                        return InkWell(
                          onTap: () {
                            onItemSelected(item);
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(15),
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  width: 1,
                                  color: AppColors.shadow.withValues(alpha: 0.1),
                                ),
                              ),
                              color: item['Id'] == selectedId ||
                                  item['UOM_AUTOID'] == selectedId
                                  ? AppColors.primary.withValues(alpha: 0.1)
                                  : AppColors.background,
                            ),
                            child: Text(item[displayKey]),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Tạo sản phẩm'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          CustomSaveButton(onTap: () {
            save();
          })
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            Container(
              width: double.infinity,
              height: 100,
              color: AppColors.shadow.withValues(alpha: 0.1),
              padding: EdgeInsets.all(10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (image != null)
                    Padding(
                      padding: EdgeInsets.fromLTRB(0, 0, 10, 0),
                      child: Image.file(image!, fit: BoxFit.cover, width: 80, height: 80,),
                    ),
                  if (imageOnline != '' && image == null)
                    Padding(
                      padding: EdgeInsets.fromLTRB(0, 0, 10, 0),
                      child: Image.network(SmeUrl.baseImageUrl + imageOnline, fit: BoxFit.cover, width: 80, height: 80,
                        errorBuilder: (context, _, __){
                          return Image.asset('assets/images/general/no_image.jpg', fit: BoxFit.cover,);
                        },
                        loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                              child: CircularProgressIndicator(color: AppColors.primary,)
                          );
                        },
                      ),
                    ),
                  if (image != null || imageOnline != '')
                    DottedBorder(
                      options: RectDottedBorderOptions(
                        color: AppColors.shadow,
                      ),
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            image = null;
                            imageOnline = '';
                          });
                        },
                        child: Container(
                          width: 80,
                          height: 80,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(Icons.image_not_supported, color: AppColors.danger,),
                              CustomText(text: 'Xoá ảnh')
                            ],
                          ),
                        ),
                      ),
                    ),
                  if (image == null && imageOnline == '')
                    DottedBorder(
                      options: RectDottedBorderOptions(
                        color: AppColors.shadow,
                      ),
                      child: InkWell(
                        onTap: () {
                          pickImageFromGallery();
                        },
                        child: Container(
                          width: 80,
                          height: 80,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(Icons.image, color: AppColors.primary,),
                              CustomText(text: 'Thêm ảnh')
                            ],
                          ),
                        ),
                      ),
                    ),
                  if (image == null && imageOnline == '')
                    Padding(
                      padding: EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: DottedBorder(
                        options: RectDottedBorderOptions(
                            color: AppColors.shadow
                        ),
                        child: InkWell(
                          onTap: () {
                            pickImageFromCamera();
                          },
                          child: Container(
                            width: 80,
                            height: 80,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Icon(Icons.camera, color: AppColors.primary,),
                                CustomText(text: 'Chụp ảnh')
                              ],
                            ),
                          ),
                        ),
                      ),
                    )
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.all(10),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomTextField(
                    controller: productNameController,
                    label: 'Tên sản phẩm',
                    required: true,
                  ),
                  CustomMoneyField(
                    controller: priceController,
                    label: 'Giá bán',
                    required: true,
                  ),
                  CustomMoneyField(
                    controller: originalPriceController,
                    label: 'Giá vốn',
                    required: true,
                  ),
                  CustomTextField(
                    controller: unitController,
                    label: 'Đơn vị tính',
                    readOnly: true,
                    required: true,
                    isSelect: true,
                    onTap: () {
                      pickUnit();
                    },
                  ),
                  CustomTextField(
                    controller: categoryController,
                    label: 'Danh mục',
                    readOnly: true,
                    required: true,
                    isSelect: true,
                    onTap: () {
                      pickCategory();
                    },
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Expanded(
                        child: CustomTextField(
                          controller: barCodeController,
                          label: 'Barcode',
                        ),
                      ),
                      SizedBox(width: 10),
                      Container(
                        decoration: BoxDecoration(
                          color: AppColors.background,
                          borderRadius: BorderRadius.circular(10),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                        child: IconButton(
                          icon: Icon(Icons.qr_code_scanner, color: AppColors.text),
                          onPressed: () async {
                            await scanBarcode();
                          },
                        ),
                      ),
                    ],
                  ),
                  CustomMoneyField(
                    controller: vatPercentController,
                    label: 'Phần trăm VAT',
                    required: true,
                    decimal: 1,
                  ),
                  SizedBox(height: 5),
                  InkWell(
                    onTap: () {
                      setState(() {
                        includeVat = !includeVat;
                      });
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          width: 25,
                          height: 25,
                          margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                          decoration: BoxDecoration(
                              border: Border.all(
                                  width: 2,
                                  color: includeVat ? AppColors.primary : AppColors.shadow
                              ),
                              borderRadius: BorderRadius.circular(5),
                              color: includeVat ? AppColors.primary : Colors.white
                          ),
                          child: includeVat ? Center(child: Icon(Icons.check, color: Colors.white, size: 22,)) : null,
                        ),
                        CustomText(text: 'Đã bao gồm VAT', bold: true,)
                      ],
                    ),
                  ),
                  SizedBox(height: 10,),
                  CustomText(text: 'Tình trạng sản phẩm'),
                  Row(
                    children: [
                      Container(
                        width: 150,
                        margin: EdgeInsets.fromLTRB(0, 0, 10, 10),
                        child: CustomButton(
                          text: 'Còn hàng',
                          color: availableStatus == 'AVAILABLE' ? AppColors.primary : AppColors.shadow,
                          onTap: () {
                            setState(() {
                              availableStatus = 'AVAILABLE';
                            });
                          },
                        ),
                      ),
                      Container(
                        width: 150,
                        margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                        child: CustomButton(
                          text: 'Hết hàng',
                          color: availableStatus == 'UNAVAILABLE' ? AppColors.primary : AppColors.shadow,
                          onTap: () {
                            setState(() {
                              availableStatus = 'UNAVAILABLE';
                            });
                          },
                        ),
                      )
                    ],
                  ),
                  InkWell(
                    onTap: () {
                      setState(() {
                        isActive = !isActive;
                      });
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          width: 25,
                          height: 25,
                          margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                          decoration: BoxDecoration(
                              border: Border.all(
                                  width: 2,
                                  color: isActive ? AppColors.primary : AppColors.shadow
                              ),
                              borderRadius: BorderRadius.circular(5),
                              color: isActive ? AppColors.primary : Colors.white
                          ),
                          child: isActive ? Center(child: Icon(Icons.check, color: Colors.white, size: 22,)) : null,
                        ),
                        CustomText(text: 'Kích hoạt', bold: true)
                      ],
                    ),
                  ),
                  SizedBox(height: 50),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
