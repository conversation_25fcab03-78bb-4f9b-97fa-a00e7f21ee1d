import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/product_controller.dart';
import 'package:gls_self_order/general/views/product/product_create_or_update_view.dart';
import 'package:intl/intl.dart';

class ProductView extends StatefulWidget {
  const ProductView({super.key});

  @override
  State<ProductView> createState() => _ProductViewState();
}

class _ProductViewState extends State<ProductView> with SingleTickerProviderStateMixin {
  //variable
  late TabController tabController;
  int currentTab = 0;
  ProductController productController = Get.find<ProductController>();
  List productWithCategories = [];
  List categories = [];
  final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '', decimalDigits: 0);

  //function
  @override
  void initState() {
    super.initState();
    tabController = TabController(length: 2, vsync: this);
    getData();
  }

  getData() async {
    AppFunction.showLoading();
    await productController.getSaleMenu();
    await getProductWithCategories();
    AppFunction.hideLoading();
  }

  getProductWithCategories() async {
    productWithCategories = await productController.getProductWithCategories();
    setState(() {

    });
  }

  getCategories() async {
    categories = await productController.getCategories();
    setState(() {

    });
  }

  onChangeTab(tab) async {
    if (tab != currentTab) {
      currentTab = tab;
      if (tab == 0) {
        await getProductWithCategories();
      }
      else {
        await getCategories();
      }
    }
  }

  Widget renderListProduct() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Container(
        //   width: double.infinity,
        //   padding: EdgeInsets.all(5),
        //   child: Row(
        //     children: [
        //       Expanded(
        //         child: CustomTextField(
        //           controller: TextEditingController(),
        //           showLabel: false,
        //           hint: 'Tìm kiếm sản phẩm',
        //           space: false,
        //         ),
        //       ),
        //       InkWell(
        //         onTap: () {
        //
        //         },
        //         child: Container(
        //           width: 45,
        //           height: 45,
        //           margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
        //           decoration: BoxDecoration(
        //               color: AppColors.primary,
        //               borderRadius: BorderRadius.circular(10)
        //           ),
        //           child: Icon(Icons.search, color: Colors.white,),
        //         ),
        //       ),
        //     ],
        //   ),
        // ),
        Expanded(
          child: ListView(
            padding: EdgeInsets.all(10),
            children: [
              for (dynamic itemCate in productWithCategories)
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            width: 1,
                            color: AppColors.shadow.withValues(alpha: 0.2)
                          )
                        )
                      ),
                      margin: EdgeInsets.fromLTRB(0, 0, 0, 5),
                      child: CustomText(text: itemCate['ItemGroupName'], bold: true, size: 18,),
                    ),
                    for (dynamic itemProd in itemCate['Items'])
                      InkWell(
                        onTap: () {
                          showDialog(
                              context: context,
                              builder: (context) {
                                return AlertDialog(
                                  backgroundColor: Colors.transparent,
                                  content: SizedBox(
                                    width: double.infinity,
                                    height: 150,
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        CustomButton(
                                            text: 'Chỉnh sửa',
                                            onTap: () async {
                                              Get.back();
                                              final result = await Get.to(() => ProductCreateOrUpdateView(item: itemProd,));
                                              if (result != null) {
                                                getProductWithCategories();
                                              }
                                            },
                                          color: Colors.white,
                                          textColor: AppColors.primary,
                                          borderColor: AppColors.primary,
                                          fontSize: 18,
                                        ),
                                        SizedBox(height: 10,),
                                        CustomButton(
                                            text: 'Xoá',
                                            onTap: () {
                                              Get.back();
                                              showDialog(
                                                  context: context,
                                                  builder: (context) {
                                                    return AlertDialog(
                                                      title: CustomText(text: 'Bạn có chắc muốn xoá sản phẩm này?', maxLines: 2,),
                                                      actions: [
                                                        Container(
                                                          width: 100,
                                                          margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                                                          child: CustomButton(
                                                            text: 'cancel'.tr,
                                                            color: AppColors.shadow,
                                                            onTap: () {
                                                              Get.back();
                                                            },
                                                          ),
                                                        ),
                                                        Container(
                                                          width: 100,
                                                          margin: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                                          child: CustomButton(
                                                            text: 'confirm'.tr,
                                                            color: AppColors.primary,
                                                            onTap: () async {
                                                              AppFunction.showLoading();
                                                              bool success = await productController.postDeleteProduct(itemProd['Id']);
                                                              if (success) {
                                                                Get.back();
                                                                getProductWithCategories();
                                                              }
                                                              AppFunction.hideLoading();
                                                            },
                                                          ),
                                                        ),
                                                      ],
                                                    );
                                                  }
                                              );
                                            },
                                            color: AppColors.danger,
                                          fontSize: 18,
                                        )
                                      ],
                                    ),
                                  ),
                                );
                              }
                          );
                        },
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: AppColors.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(10),
                            boxShadow: [
                              BoxShadow(
                                  color: Colors.grey.withValues(alpha: 0.05),
                                  spreadRadius: 0,
                                  blurRadius: 1,
                                  offset: Offset(0, 3)
                              ),
                            ],
                          ),
                          clipBehavior: Clip.antiAlias,
                          margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                width: 100,
                                height: 100,
                                child: itemProd['Images'].isNotEmpty 
                                    ? Image.network(SmeUrl.baseImageUrl + itemProd['Images'][itemProd['Images'].length - 1]['MediaUrl'], fit: BoxFit.cover,
                                        errorBuilder: (context, _, __){
                                          return Image.asset('assets/images/general/no_image.jpg', fit: BoxFit.cover,);
                                        },
                                        loadingBuilder: (BuildContext context, Widget child, ImageChunkEvent? loadingProgress) {
                                          if (loadingProgress == null) return child;
                                          return Center(
                                              child: CircularProgressIndicator(color: AppColors.primary,)
                                          );
                                        },
                                      )
                                    : Image.asset('assets/images/general/no_image.jpg'),
                              ),
                              Expanded(
                                child: Padding(
                                  padding: EdgeInsets.fromLTRB(10, 2.5, 10, 2.5),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      CustomText(text: itemProd['ItemName'], bold: true,),
                                      CustomText(text: itemCate['ItemGroupName'], italic: true,),
                                      CustomText(text: 'Giá: ${currencyFormat.format(itemProd['Price'])}/${itemProd['UnitName']}'),
                                      CustomText(text: itemProd['AvailableStatus'] == 'AVAILABLE' ? 'Còn hàng' : 'Hết hàng', bold: true, color: itemProd['AvailableStatus'] == 'AVAILABLE' ? AppColors.button : AppColors.danger,),
                                    ],
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      )
                  ],
                )
            ],
          ),
        )
      ],
    );
  }

  Widget renderListCategory() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Container(
        //   width: double.infinity,
        //   padding: EdgeInsets.all(5),
        //   child: Row(
        //     children: [
        //       Expanded(
        //         child: CustomTextField(
        //           controller: TextEditingController(),
        //           showLabel: false,
        //           hint: 'Tìm kiếm danh mục',
        //           space: false,
        //         ),
        //       ),
        //       InkWell(
        //         onTap: () {
        //
        //         },
        //         child: Container(
        //           width: 45,
        //           height: 45,
        //           margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
        //           decoration: BoxDecoration(
        //               color: AppColors.primary,
        //               borderRadius: BorderRadius.circular(10)
        //           ),
        //           child: Icon(Icons.search, color: Colors.white,),
        //         ),
        //       ),
        //     ],
        //   ),
        // ),
        Expanded(
          child: ListView(
            padding: EdgeInsets.all(10),
            children: [
              for(dynamic item in categories)
                InkWell(
                  onTap: () {
                    showDialog(
                        context: context,
                        builder: (context) {
                          return AlertDialog(
                            backgroundColor: Colors.transparent,
                            content: SizedBox(
                              width: double.infinity,
                              height: 150,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  CustomButton(
                                      text: 'Chỉnh sửa',
                                      onTap: () {
                                        Get.back();
                                        showCreateOrUpdateCategory(item);
                                      },
                                    color: Colors.white,
                                    textColor: AppColors.primary,
                                    borderColor: AppColors.primary,
                                    fontSize: 18,
                                  ),
                                  SizedBox(height: 10,),
                                  CustomButton(
                                      text: 'Xoá',
                                      onTap: () {
                                        Get.back();
                                        showDialog(
                                            context: context,
                                            builder: (context) {
                                              return AlertDialog(
                                                title: CustomText(text: 'Bạn có chắc muốn xoá danh mục này?'),
                                                actions: [
                                                  Container(
                                                    width: 100,
                                                    margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                                                    child: CustomButton(
                                                      text: 'cancel'.tr,
                                                      color: AppColors.shadow,
                                                      onTap: () {
                                                        Get.back();
                                                      },
                                                    ),
                                                  ),
                                                  Container(
                                                    width: 100,
                                                    margin: EdgeInsets.fromLTRB(0, 0, 0, 0),
                                                    child: CustomButton(
                                                      text: 'confirm'.tr,
                                                      color: AppColors.primary,
                                                      onTap: () async {
                                                        bool success = await productController.postDeleteCategory(item['Id']);
                                                        if (success) {
                                                          Get.back();
                                                          getCategories();
                                                        }
                                                      },
                                                    ),
                                                  ),
                                                ],
                                              );
                                            }
                                        );
                                      },
                                      color: AppColors.danger,
                                    fontSize: 18,
                                  )
                                ],
                              ),
                            ),
                          );
                        }
                    );
                  },
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                            color: Colors.grey.withOpacity(0.05),
                            spreadRadius: 0,
                            blurRadius: 1,
                            offset: Offset(0, 3)
                        ),
                      ],
                    ),
                    padding: EdgeInsets.all(10),
                    margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(text: item['ItemGroupName'], bold: true,),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                )
            ],
          ),
        )
      ],
    );
  }

  showCreateOrUpdateCategory(item) {
    TextEditingController nameController = TextEditingController();
    if (item != null) {
      nameController.text = item['ItemGroupName'];
    }
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
          insetPadding: EdgeInsets.all(10),
          backgroundColor: Colors.white,
          contentPadding: EdgeInsets.fromLTRB(10, 30, 10, 30),
          content: Container(
            width: MediaQuery.of(context).size.width,
            height: 150,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10)
            ),
            padding: EdgeInsets.all(5),
            child: Column(
              children: [
                CustomTextField(
                  label: 'Tên danh mục',
                  controller: nameController,
                  required: true,
                ),
                SizedBox(height: 7,),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      width: 100,
                      height: 50,
                      child: CustomButton(
                          text: 'Huỷ',
                          onTap: () {
                            Get.back();
                          },
                          color: AppColors.shadow
                      ),
                    ),
                    Container(
                      width: 100,
                      height: 50,
                      margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
                      child: CustomButton(
                          text: 'Lưu',
                          onTap: () async {
                            AppFunction.showLoading();
                            String name = nameController.text;
                            bool success = await productController.postCreateOrUpdateCategory(item != null ? item['Id'] : null, name);
                            if (success) {
                              Get.back();
                              getCategories();
                            }
                            AppFunction.hideLoading();
                          },
                          color: AppColors.primary
                      ),
                    )
                  ],
                )
              ],
            ),
          ),
        );
      }
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Quản lý sản phẩm'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          InkWell(
            onTap: () async {
              if (tabController.index == 0) {
                await Get.to(() => ProductCreateOrUpdateView());
                getProductWithCategories();
              }
              else {
                showCreateOrUpdateCategory(null);
              }
            },
            child: const Padding(
              padding: EdgeInsets.all(10),
              child: Icon(Icons.add, color: Colors.white, size: 35,),
            ),
          )
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          children: [
            TabBar(
              controller: tabController,
              tabs: [
                Tab(text: 'Sản phẩm'),
                Tab(text: 'Danh mục'),
              ],
              labelColor: AppColors.primary,
              indicatorColor: AppColors.primary,
              dividerHeight: 0.1,
              onTap: (tab) {
                onChangeTab(tab);
              },
            ),
            Expanded(
              child: TabBarView(
                controller: tabController,
                children: [
                  renderListProduct(),
                  renderListCategory(),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
