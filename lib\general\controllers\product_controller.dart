import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/constants/sme_url.dart';
import 'package:gls_self_order/core/network/dio_client.dart';
import 'package:gls_self_order/core/utils/toast_info.dart';
import 'package:path/path.dart';
import 'dart:io';
import 'package:dio/dio.dart' as DIO;

class ProductController extends GetxController {
  //variable
  final DioClientSME dioClient = DioClientSME();
  int saleMenuId = 0;

  //function
  getSaleMenu() async {
    try {
      final response = await dioClient.get(
          SmeUrl.menuTree,
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null && data['Result']['TimeSaleMenus'] != null) {
        List menus = data['Result']['TimeSaleMenus'];
        if (menus.isNotEmpty) {
          saleMenuId = menus[0]['TimeSaleAutoId'];
        }
      }
    } catch (e) {

    }
  }

  getUnits() async {
    List list = [];
    try {
      final response = await dioClient.get(
        '${SmeUrl.units}',
      );
      dynamic data = response.data;
      if(data['Success'] && data['Result'] != null) {
        list = data['Result'];
      }
    } catch (e) {

    }
    return list;
  }

  getCategories() async {
    List list = [];
    try {
      final response = await dioClient.get(
        '${SmeUrl.groups}?timeSaleId=$saleMenuId',
      );
      dynamic data = response.data;
      if(data['Success'] && data['Result'] != null) {
        list = data['Result'];
      }
    } catch (e) {

    }
    return list;
  }

  getProductWithCategories() async {
    List list = [];
    try {
      final response = await dioClient.get(
        SmeUrl.menuTree,
      );
      dynamic data = response.data;
      if(data['Success'] && data['Result'] != null && data['Result']['TimeSaleMenus'] != null && data['Result']['TimeSaleMenus'].isNotEmpty) {
        list = data['Result']['TimeSaleMenus'][0]['ItemGroups'];
      }
    } catch (e) {

    }
    return list;
  }

  postCreateOrUpdateCategory(id, String name) async {
    if (name.isEmpty) {
      AppFunction.showError('Vui lòng nhập tên danh mục');
      return false;
    }
    dynamic body = {
      "Id": id,
      "TimeSaleAutoId": saleMenuId,
      "ItemGroupName": name,
      "AvailableStatus": "AVAILABLE",
      "IsActive": true,
    };

    try {
      final response = await dioClient.post(
          SmeUrl.createOrUpdateGroup,
          data: body
      );
      dynamic data = response.data;
      if (data['Success']) {
        AppFunction.showSuccess(data['Message']);
        return true;
      }
      else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
      return false;
    }
  }

  postDeleteCategory(id) async {
    try {
      final response = await dioClient.delete(
          '${SmeUrl.deleteGroup}?Id=$id',
      );
      dynamic data = response;
      if (data['Success']) {
        AppFunction.showSuccess(data['Message']);
        return true;
      }
      else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
      return false;
    }
  }

  getDetailProduct(id) async {
    dynamic item;
    try {
      final response = await dioClient.get(
        '${SmeUrl.detailItem}?ItemId=$id',
      );
      dynamic data = response.data;
      if(data['Success'] && data['Result'] != null) {
        item = data['Result'];
      }
    } catch (e) {

    }
    return item;
  }

  postCreateOrUpdateProduct(id, name, originalPrice, price, categoryId, vatPercent, status, includeVat, unitId, unitName, barcode, isActive, itemNo) async {
    int productId = 0;
    if (name.isEmpty) {
      AppFunction.showError('Vui lòng nhập tên sản phẩm');
      return productId;
    }
    if (price.isEmpty) {
      AppFunction.showError('Vui lòng nhập giá bán sản phẩm');
      return productId;
    }
    if (originalPrice.isEmpty) {
      AppFunction.showError('Vui lòng nhập giá vốn sản phẩm');
      return productId;
    }
    if (unitId == 0) {
      AppFunction.showError('Vui lòng chọn đơn vị tính');
      return productId;
    }
    if (categoryId == 0) {
      AppFunction.showError('Vui lòng chọn danh mục');
      return productId;
    }

    // Validate VAT
    if (vatPercent.isEmpty || vatPercent == '0.0') {
      AppFunction.showError('Vui lòng nhập phần trăm VAT');
      return productId;
    }

    dynamic body = {
      "Id": id,
      // "MenuId": null,
      "TimeSaleMenuId": saleMenuId,
      "ItemGroupMapperAutoId": categoryId,
      "ItemGroupId": categoryId,
      "ItemName": name,
      "ItemNo": itemNo != '' ? itemNo : AppFunction.generateRandomString(10),
      "Barcode": barcode,
      "IsCombo": false,
      "UnitId": unitId,
      "UnitName": unitName,
      "VatPercent": vatPercent,
      "CostPrice": originalPrice,
      "Price": price,
      "IncludeVat": includeVat,
      "AvailableStatus": status,
      "IsActive": isActive,
    };
    //
    // // Log request before sending
    // print('======= START REQUEST TO CREATE/UPDATE PRODUCT =======');
    // print('URL: ${SmeUrl.createOrUpdateItem}');
    // print('Method: POST');
    // //print('Headers: ${dioClient.dio.options.headers}');
    // print('Body: $body');
    // print('=====================================================');


    try {
      final response = await dioClient.post(
          SmeUrl.createOrUpdateItem,
          data: body
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        productId = data['Result']['Id'];
        AppFunction.showSuccess(data['Message']);
      }
      else {
        AppFunction.showError(data['Message']);
      }
    } catch (e) {
      AppFunction.showError('Lỗi thêm sản phẩm');
    }
    return productId;
  }

  postDeleteProduct(id) async {
    try {
      final response = await dioClient.delete(
        '${SmeUrl.deleteItem}?Id=$id',
      );
      dynamic data = response;
      if (data['Success']) {
        AppFunction.showSuccess(data['Message']);
        return true;
      }
      else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Lỗi không xác định');
      return false;
    }
  }

  postUploadImageProduct(image) async {
    String path = '';

    String fileName = basename(image.path);
    DIO.FormData formData = DIO.FormData.fromMap({
      'file': await DIO.MultipartFile.fromFile(image.path, filename: fileName),
    });

    try {
      final response = await dioClient.post(
        SmeUrl.uploadImage,
        data: formData,
        options: DIO.Options(
          contentType: 'multipart/form-data',
        ),
      );
      dynamic data = response.data;
      if (data['Success'] && data['Result'] != null) {
        AppFunction.showSuccess(data['Message']);
        path = data['Result']['FileUrl'];
      }
      else {
        AppFunction.showError(data['Message']);
      }
    } catch (e) {
      AppFunction.showError(e.toString());
    }
    return path;
  }

  postAddImageForProduct(productId, imagePath) async {
    try {
      final response = await dioClient.post(
        SmeUrl.createProductImage,
        data: {
          "MediaUrl": imagePath,
          "ItemMapperAutoId": productId,
          "IsActive": true,
        }
      );
      dynamic data = response.data;

      if (data['Success']) {
        AppFunction.showSuccess(data['Message']);
        return true;
      }
      else {
        AppFunction.showError(data['Message']);
        return false;
      }
    } catch (e) {
      AppFunction.showError('Có lỗi xảy ra khi gán ảnh');
      return false;
    }
  }
}