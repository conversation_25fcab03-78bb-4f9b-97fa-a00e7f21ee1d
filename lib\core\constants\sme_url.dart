class SmeUrl {
  //static const baseURL = 'https://api-sme-dev.goldensme.com/api';
  static const baseURL = 'https://api-sme-center.goldensme.com/api';
  //static const baseImageUrl  = 'https://api-sme-dev.goldensme.com';
  static const baseImageUrl  = 'https://api-sme-center.goldensme.com';
  static const genBillUrl = 'https://cms-api.goldensme.com/api/invoice/generate-image';

  static const login = '$baseURL/core/auth/login';
  static const userInfo = '$baseURL/core/auth/info';
  static const register = '$baseURL/sme/customer/init-org';
  static const changePassword = '$baseURL/core/auth/password';
  static const activeLicense = '$baseURL/sme/license/assign-to-org';

  static const menuInfo = '$baseURL/sale/sme/menu';
  static const menuTree = '$baseURL/sale/sme/menu-tree';
  static const groups = '$baseURL/sale/sme/groups';
  static const createOrUpdateGroup = '$baseURL/sale/sme/item-group-mapper-create';
  static const deleteGroup = '$baseURL/sale/sme/item-group-mapper-delete';
  static const detailItem = '$baseURL/sale/sme/item-mapper-detail';
  static const createOrUpdateItem = '$baseURL/sale/sme/item-mapper-create';
  static const createProductImage = '$baseURL/sale/sme/item-media-create';
  static const deleteItem = '$baseURL/sale/sme/item-mapper-delete';

  //config einvoice
  static const invoiceInfo = '$baseURL/sme/einvoice/loadbybranch';
  static const invoiceInsert = '$baseURL/sme/einvoice/insert';
  static const invoiceUpdate = '$baseURL/sme/einvoiceupdate';

  //unit
  static const units = '$baseURL/core/common/uom/list';

  //receipt
  static const receiptList = '$baseURL/sme/receipt/list';
  static const detailReceipt = '$baseURL/sme/receipt/detail';
  static const createOrUpdateReceipt = '$baseURL/sme/receipt/create-update';
  static const deleteReceipt = '$baseURL/sme/receipt/delete';
  static const receiptItemList = '$baseURL/sme/receipt/receipt-item-list';
  static const receiptGroupList = '$baseURL/sme/receipt/receipt-group-list';

  //order
  static const orderList = '$baseURL/sale/sme/orders';
  static const orderGroupByCustomerList = '$baseURL/sale/sme/customer/order';
  static const statusList = '$baseURL/sale/sme/status-list';
  static const orderDetail = '$baseURL/sale/sme/order';
  static const orderCreateInvoice = '$baseURL/sale/sme/invoice';
  static const order = '$baseURL/sale/sme/order';
  static const generateZALOQRSME = '$baseURL/sale/sme/zalo/payment';
  static const generateACBQRSME = '$baseURL/sale/sme/acb/payment';
  static const updateOrderStatusSME = '$baseURL/sale/sme/status';

  //upload
  static const uploadImage = '$baseURL/upload/image';

  //general
  static const paymentMethodList = '$baseURL/core/common/payment-method';
  static const businessTypeList = '$baseURL/core/common/business-type';

  //license
  static const verifyLicense = '$baseURL/sme/license/detail';
  static const branchByLicense = '$baseURL/sme/license/load-branches';

  //revenue
  static const branches = '$baseURL/core/common/branches';
  static const revenueOverview = '$baseURL/sme/report/dashboard/overview';

  //customer
  static const customerList = '$baseURL/sme/customer/list';
  static const customerDetail = '$baseURL/sme/customer/detail';
  static const customerCreate = '$baseURL/sme/customer/create';
  static const customerUpdate = '$baseURL/sme/customer/update';
  static const customerDelete = '$baseURL/sme/customer/delete';

  //payment
  static const paymentList = '$baseURL/sme/transaction/payment-transaction/filter';

  // ACB
  static const acbRegister = '$baseURL/sme/acb/register';
  static const acbGetByOrg = '$baseURL/sme/acb/getbyorg';
  static const acbRegisterVerify = '$baseURL/sme/acb/register/verify';
  static const acbUpdateVirtualAccount = '$baseURL/sme/acb/UpdateVirtualAccount';
  static const acbUseAccount = '$baseURL/sme/acb/UseAccount';
  static const acbCancel = '$baseURL/sme/acb/cancel';
  static const acbCancelVerify = '$baseURL/sme/acb/cancel/verify';

  // Info
  static const orgInfo = '$baseURL/sme/org/info';
  static const orgUpdate = '$baseURL/sme/org/update';
}