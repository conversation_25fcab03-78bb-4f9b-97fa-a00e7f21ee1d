import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/customer_controller.dart';
import 'package:gls_self_order/general/views/customer/customer_create_or_update_view.dart';
import 'package:gls_self_order/general/views/manufacture/manufacture_create_or_update_view.dart';

class ManufactureView extends StatefulWidget {
  const ManufactureView({super.key});

  @override
  State<ManufactureView> createState() => _ManufactureViewState();
}

class _ManufactureViewState extends State<ManufactureView> {
  //variable
  CustomerController customerController = Get.find();
  TextEditingController searchController = TextEditingController();
  List customerList = [];
  dynamic customerPagination = {
    'current_page': 1,
    'page_size': 50,
    'total_page': 0,
    'total_records': 0,
    'item_count': 0
  };

  //function
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getData();
  }

  getData() async {
    AppFunction.showLoading();
    List resp = await customerController.getList(
        customerPagination['current_page'],
        customerPagination['page_size'],
        '2025-06-01',
        '2077-01-01',
        searchController.text
    );
    customerList = resp[0];
    dynamic pagination = resp[1];
    if (pagination != null) {
      customerPagination['total_page'] = pagination['TotalPages'];
      customerPagination['total_records'] = pagination['TotalRecords'];
      customerPagination['item_count'] = pagination['ItemCount'];
    }
    setState(() {

    });
    AppFunction.hideLoading();
  }

  Widget renderFilter() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(5),
      child: Row(
        children: [
          Expanded(
            child: CustomTextField(
              controller: searchController,
              showLabel: false,
              hint: 'Tìm kiếm nhà cung cấp',
              space: false,
            ),
          ),
          InkWell(
            onTap: () async {
              await getData();
            },
            child: Container(
              width: 45,
              height: 45,
              margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
              decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(10)
              ),
              child: Icon(Icons.search, color: Colors.white,),
            ),
          ),
        ],
      ),
    );
  }

  Widget renderItem(item) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withValues(alpha: 0.05),
              spreadRadius: 0,
              blurRadius: 1,
              offset: Offset(0, 3)
          ),
        ],
      ),
      padding: EdgeInsets.all(10),
      margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Container(
              //   width: 50,
              //   height: 50,
              //   decoration: BoxDecoration(
              //     borderRadius: BorderRadius.circular(180)
              //   ),
              //   margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
              //   clipBehavior: Clip.antiAlias,
              //   child: Image.asset('assets/images/general/avatar.jpg'),
              // ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(child: CustomText(text: item['FullName'] ?? '', bold: true, size: 18,),),
                        // CustomText(text: '15.050.000', color: Colors.blue,)
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        CustomText(text: '📞${item['PhoneNumber'] ?? ''}'),
                        // CustomText(text: 'Tổng đơn 5/2 hoá đơn')
                      ],
                    )
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Nhà cung cấp'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          InkWell(
            onTap: () async {
              final result = await Get.to(() => ManufactureCreateOrUpdateView(item: null,));
              if (result != null) {
                getData();
              }
            },
            child: const Padding(
              padding: EdgeInsets.all(10),
              child: Icon(Icons.add, color: Colors.white, size: 35,),
            ),
          )
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          children: [
            //for filter
            renderFilter(),
            Expanded(
              child: ListView(
                padding: EdgeInsets.all(10),
                children: [
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.05),
                            spreadRadius: 0,
                            blurRadius: 1,
                            offset: Offset(0, 3)
                        ),
                      ],
                    ),
                    padding: EdgeInsets.all(10),
                    margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            // Container(
                            //   width: 50,
                            //   height: 50,
                            //   decoration: BoxDecoration(
                            //     borderRadius: BorderRadius.circular(180)
                            //   ),
                            //   margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                            //   clipBehavior: Clip.antiAlias,
                            //   child: Image.asset('assets/images/general/avatar.jpg'),
                            // ),
                            Expanded(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Expanded(child: CustomText(text: 'Công ty thực phẩm Miền Nam', bold: true, size: 18,),),
                                      // CustomText(text: '15.050.000', color: Colors.blue,)
                                    ],
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      CustomText(text: '📞0913789789'),
                                      // CustomText(text: 'Tổng đơn 5/2 hoá đơn')
                                    ],
                                  )
                                ],
                              ),
                            )
                          ],
                        ),
                      ],
                    ),
                  )
                  // for(dynamic item in customerList)
                  //   InkWell(
                  //     onTap: () {
                  //       showDialog(
                  //           context: context,
                  //           builder: (context) {
                  //             return AlertDialog(
                  //               backgroundColor: Colors.transparent,
                  //               content: SizedBox(
                  //                 width: double.infinity,
                  //                 height: 150,
                  //                 child: Column(
                  //                   mainAxisAlignment: MainAxisAlignment.center,
                  //                   children: [
                  //                     CustomButton(
                  //                         text: 'Chỉnh sửa',
                  //                         onTap: () async {
                  //                           Get.back();
                  //                           final result = await Get.to(() => ManufactureCreateOrUpdateView(item: item,));
                  //                           if (result != null) {
                  //                             getData();
                  //                           }
                  //                         },
                  //                         color: AppColors.primary
                  //                     ),
                  //                     // SizedBox(height: 10,),
                  //                     // CustomButton(
                  //                     //     text: 'Xoá',
                  //                     //     onTap: () {
                  //                     //       Get.back();
                  //                     //       showDialog(
                  //                     //           context: context,
                  //                     //           builder: (context) {
                  //                     //             return AlertDialog(
                  //                     //               title: CustomText(text: 'Bạn có chắc muốn xoá phiếu thu này?', maxLines: 2,),
                  //                     //               actions: [
                  //                     //                 Container(
                  //                     //                   width: 100,
                  //                     //                   margin: EdgeInsets.fromLTRB(0, 0, 5, 0),
                  //                     //                   child: CustomButton(
                  //                     //                     text: 'cancel'.tr,
                  //                     //                     color: AppColors.shadow,
                  //                     //                     onTap: () {
                  //                     //                       Get.back();
                  //                     //                     },
                  //                     //                   ),
                  //                     //                 ),
                  //                     //                 Container(
                  //                     //                   width: 100,
                  //                     //                   margin: EdgeInsets.fromLTRB(0, 0, 0, 0),
                  //                     //                   child: CustomButton(
                  //                     //                     text: 'confirm'.tr,
                  //                     //                     color: AppColors.primary,
                  //                     //                     onTap: () async {
                  //                     //                       bool success = await receiptController.postDelete(item['Id']);
                  //                     //                       if (success) {
                  //                     //                         Get.back();
                  //                     //                         getData();
                  //                     //                       }
                  //                     //                     },
                  //                     //                   ),
                  //                     //                 ),
                  //                     //               ],
                  //                     //             );
                  //                     //           }
                  //                     //       );
                  //                     //     },
                  //                     //     color: AppColors.danger
                  //                     // )
                  //                   ],
                  //                 ),
                  //               ),
                  //             );
                  //           }
                  //       );
                  //     },
                  //     child: renderItem(item),
                  //   )
                ],
              ),
            ),
            // Container(
            //   width: double.infinity,
            //   decoration: BoxDecoration(
            //     color: Colors.white,
            //     boxShadow: [
            //       BoxShadow(
            //           color: Colors.grey.withValues(alpha: 0.05),
            //           spreadRadius: 0,
            //           blurRadius: 1,
            //           offset: Offset(0, -3)
            //       ),
            //     ],
            //   ),
            //   padding: EdgeInsets.all(10),
            //   margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
            //   child: Row(
            //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //     crossAxisAlignment: CrossAxisAlignment.center,
            //     children: [
            //       CustomText(text: 'Đang hiển thị từ  ${(customerPagination['current_page'] - 1) * customerPagination['page_size'] + 1} - ${(customerPagination['current_page'] - 1) * customerPagination['page_size'] + customerPagination['item_count']}',),
            //       Row(
            //         children: [
            //           InkWell(
            //             onTap: () async {
            //               if (customerPagination['current_page'] > 1) {
            //                 customerPagination['current_page'] -= 1;
            //                 await getData();
            //               }
            //             },
            //             child: Icon(Icons.arrow_back_ios, color: customerPagination['current_page'] > 1 ? Colors.black : AppColors.shadow,),
            //           ),
            //           CustomText(text: '   ${customerPagination['current_page']}   ', bold: true,),
            //           InkWell(
            //             onTap: () async {
            //               if (customerPagination['current_page'] < customerPagination['total_page']) {
            //                 customerPagination['current_page'] += 1;
            //                 await getData();
            //               }
            //             },
            //             child: Icon(Icons.arrow_forward_ios, color: customerPagination['current_page'] < customerPagination['total_page'] ? Colors.black : AppColors.shadow,),
            //           ),
            //         ],
            //       )
            //     ],
            //   ),
            // )
          ],
        ),
      ),
    );
  }
}
