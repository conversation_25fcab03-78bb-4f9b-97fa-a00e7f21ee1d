import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/auth_controller.dart';
import 'package:gls_self_order/general/controllers/general_controller.dart';
import 'package:shared_preferences/shared_preferences.dart';

class RegisterView extends StatefulWidget {
  final String type;
  const RegisterView({super.key, required this.type});

  @override
  State<RegisterView> createState() => _RegisterViewState();
}

class _RegisterViewState extends State<RegisterView> {
  //variable
  GeneralController generalController = Get.find();
  AuthController authController = Get.find();
  TextEditingController orgNameController = TextEditingController();
  TextEditingController branchNameController = TextEditingController();
  TextEditingController userNameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  TextEditingController fullNameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController licenseKeyController = TextEditingController();
  TextEditingController businessTypeController = TextEditingController();
  TextEditingController businessType1Controller = TextEditingController();
  List businessTypes = [];
  int businessTypeSelected = 0;

  //function
  @override
  void initState() {
    super.initState();
    getData();
  }

  getData() async {
    businessTypes = await generalController.getBusinessTypeList();
    setState(() {

    });
  }

  pickBusinessType() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
        ),
        builder: (context) {
          return SizedBox(
            width: double.infinity,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                for(dynamic item in businessTypes)
                  InkWell(
                    onTap: () {
                      businessTypeSelected = item['Id'];
                      businessTypeController.text = item['Name'];
                      setState(() {

                      });
                      Get.back();
                    },
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1,
                                  color: AppColors.shadow.withValues(alpha: 0.1)
                              )
                          ),
                          color: item['Id'] == businessTypeSelected ? AppColors.primary.withValues(alpha: 0.1) : Colors.white
                      ),
                      padding: EdgeInsets.all(15),
                      child: CustomText(text: item['Name']),
                    ),
                  )
              ],
            ),
          );
        }
    );
  }

  register() async {
    AppFunction.showLoading();
    dynamic result = await authController.register(
        licenseKeyController.text,
        orgNameController.text,
        branchNameController.text,
        userNameController.text,
        passwordController.text,
        confirmPasswordController.text,
        fullNameController.text,
        phoneController.text,
        widget.type,
      businessTypeSelected,
      businessType1Controller.text
    );
    AppFunction.hideLoading();

    if (result['success']) {
      SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
      sharedPreferences.setString('account', jsonEncode({
        'username': result['username'],
        'password': result['password'],
      }));
      Get.back();
      Get.back();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Đăng ký tài khoản'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        padding: EdgeInsets.all(10),
        child: Column(
          children: [
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  if (widget.type == 'acb')
                    CustomTextField(
                      controller: licenseKeyController,
                      label: 'License Key',
                      required: true,
                    ),
                  CustomTextField(
                    controller: orgNameController,
                    label: 'Tên thương hiệu/đơn vị',
                    required: true,
                  ),
                  CustomTextField(
                    controller: branchNameController,
                    label: 'Tên chi nhánh/cửa hàng',
                    required: true,
                  ),
                  CustomTextField(
                    controller: businessTypeController,
                    label: 'Loại hình kinh doanh',
                    readOnly: true,
                    required: true,
                    isSelect: true,
                    onTap: () {
                      pickBusinessType();
                    },
                  ),
                  if (businessTypeSelected == 8)
                    CustomTextField(
                      controller: businessType1Controller,
                      label: 'Loại hình kinh doanh khác',
                      required: true,
                    ),
                  CustomTextField(
                    controller: fullNameController,
                    label: 'Tên của bạn',
                    required: true,
                  ),
                  CustomTextField(
                    controller: phoneController,
                    label: 'Số điện thoại',
                    required: true,
                  ),
                  CustomTextField(
                    controller: userNameController,
                    label: 'Tên đăng nhập',
                    required: true,
                  ),
                  CustomTextField(
                    controller: passwordController,
                    label: 'Mật khẩu',
                    required: true,
                    secure: true,
                  ),
                  CustomTextField(
                    controller: confirmPasswordController,
                    label: 'Nhập lại mật khẩu',
                    required: true,
                    secure: true,
                  ),
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
              child: CustomButton(text: 'Đăng ký', onTap: () {
                register();
              }, color: AppColors.primary),
            )
          ],
        ),
      ),
    );
  }
}
