import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/views/warehouse/import_warehouse_create_or_update_view.dart';

class ImportWarehouseView extends StatefulWidget {
  const ImportWarehouseView({super.key});

  @override
  State<ImportWarehouseView> createState() => _ImportWarehouseViewState();
}

class _ImportWarehouseViewState extends State<ImportWarehouseView> {
  //variable
  TextEditingController searchController = TextEditingController();

  //function
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  Widget renderFilter() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(5),
      child: Row(
        children: [
          Expanded(
            child: CustomTextField(
              controller: searchController,
              showLabel: false,
              hint: 'Tìm kiếm số phiếu',
              space: false,
            ),
          ),
          InkWell(
            onTap: () {
              // filter(false);
            },
            child: Container(
              width: 45,
              height: 45,
              margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
              decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(10)
              ),
              child: Icon(Icons.search, color: Colors.white,),
            ),
          ),
          InkWell(
            onTap: () {
              // showFilter(context);
            },
            child: Container(
              width: 45,
              height: 45,
              margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
              decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(10)
              ),
              child: Icon(Icons.filter_alt_sharp, color: Colors.white,),
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Nhập kho'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
        actions: [
          InkWell(
            onTap: () async {
              Get.to(() => ImportWarehouseCreateOrUpdateView());
            },
            child: const Padding(
              padding: EdgeInsets.all(10),
              child: Icon(Icons.add, color: Colors.white, size: 35,),
            ),
          )
        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          children: [
            //for filter
            renderFilter(),
            Expanded(
              child: ListView(
                padding: EdgeInsets.all(10),
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.05),
                            spreadRadius: 0,
                            blurRadius: 1,
                            offset: Offset(0, 3)
                        ),
                      ],
                    ),
                    padding: EdgeInsets.all(10),
                    margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            CustomText(text: 'Số phiếu: '),
                            CustomText(text: 'PN20250615203015', bold: true,),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              flex: 1,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(text: 'Số tiền:'),
                                  CustomText(text: '8.560.000 đ', bold: true,),
                                ],
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(text: 'Ngày nhập'),
                                  CustomText(text: '15/06/2025 20:30', bold: true,),
                                ],
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              flex: 1,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(text: 'Ghi chú:'),
                                  CustomText(text: 'Nhập kho nguyên liệu chế biến đồ uống', bold: true, maxLines: 4,),
                                ],
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
