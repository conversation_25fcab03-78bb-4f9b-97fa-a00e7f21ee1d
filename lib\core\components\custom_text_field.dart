import 'package:flutter/material.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';

import 'package:flutter/material.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final bool showLabel;
  final String hint;
  final bool readOnly;
  final bool disabled;
  final VoidCallback? onTap;
  final TextInputType keyboard;
  final int maxLines;
  final bool space;
  final bool secure;
  final bool required;
  final bool isSelect;
  final bool isSearch;
  final IconData? prefixIcon;
  final VoidCallback? onSearchTap;
  final ValueChanged<String>? onChanged;

  const CustomTextField({
    super.key,
    required this.controller,
    this.label = '',
    this.showLabel = true,
    this.hint = '',
    this.readOnly = false,
    this.disabled = false,
    this.onTap,
    this.keyboard = TextInputType.text,
    this.maxLines = 1,
    this.space = true,
    this.secure = false,
    this.required = false,
    this.isSelect = false,
    this.isSearch = false,
    this.prefixIcon,
    this.onSearchTap,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showLabel)
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: label,
                size: 16,
              ),
              if (required)
                CustomText(
                  text: '*',
                  color: AppColors.danger,
                  size: 16,
                )
            ],
          ),
        Container(
          margin: EdgeInsets.fromLTRB(0, 2.5, 0, space ? 10 : 0),
          child: TextField(
            controller: controller,
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
              filled: true,
              fillColor: disabled
                  ? Colors.grey.withValues(alpha: 0.2)
                  : Colors.white,
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  width: 0.5,
                  color: Colors.black.withValues(alpha: 0.2),
                ),
                borderRadius: BorderRadius.circular(7.5),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  width: 0.5,
                  color: Colors.blueAccent.withValues(alpha: 0.8),
                ),
                borderRadius: BorderRadius.circular(7.5),
              ),
              hintText: hint,
              isDense: true,
              prefixIcon: prefixIcon != null
                  ? Icon(
                prefixIcon,
                size: 20,
              )
                  : null,
              suffixIcon: _buildSuffixIcon(),
            ),
            readOnly: readOnly,
            onTap: onTap,
            keyboardType: keyboard,
            maxLines: maxLines,
            obscureText: secure,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Widget? _buildSuffixIcon() {
    if (isSearch) {
      return IconButton(
        icon: const Icon(Icons.search),
        onPressed: onSearchTap,
        iconSize: 20,
      );
    } else if (isSelect) {
      return const Icon(Icons.arrow_drop_down, size: 26);
    }
    return null;
  }
}