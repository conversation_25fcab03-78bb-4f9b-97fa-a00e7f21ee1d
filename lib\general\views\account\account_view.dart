import 'package:flutter/material.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/classes/user_info.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/general/views/account/apply_evoucher_view.dart';
import 'package:gls_self_order/general/views/account/einvoice_config_view.dart';
import 'package:gls_self_order/general/views/account/license_info_view.dart';
import 'package:gls_self_order/general/views/account/security_view.dart';
import 'package:gls_self_order/general/views/account/user_info_view.dart';
import 'package:gls_self_order/general/views/users/user_view.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../sme/presentation/e_menu/cart/cart_controller.dart';
import '../auth/login_view.dart';
import 'payment_confi/bank_account_form_view.dart';

class AccountView extends StatefulWidget {
  final String from;
  const AccountView({
    super.key,
    required this.from
  });

  @override
  State<AccountView> createState() => _AccountViewState();
}

class _AccountViewState extends State<AccountView> {
  //variable

  //function
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }


  void logout() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    sharedPreferences.remove('token');
    sharedPreferences.remove('account');
    sharedPreferences.remove('license_code');
    sharedPreferences.remove('use_bio');
    sharedPreferences.remove('data_bio');
    Get.offAll(() => const LoginView());
  }

  Widget renderMenuItem(title, icon, VoidCallback view) {
    return InkWell(
      onTap: view,
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: AppColors.primary.withValues(alpha: 0.1)
        ),
        padding: EdgeInsets.all(10),
        margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(180)
              ),
              margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
              child: Icon(icon, size: 30, color: AppColors.primary,),
            ),
            Expanded(
              child: CustomText(text: title, size: 18, bold: true,),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Tài khoản'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: widget.from == 'avatar' ? CustomBackButton() : Container(),
        actions: [

        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: ListView(
          padding: EdgeInsets.all(10),
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 100,
                  height: 100,
                  margin: EdgeInsets.fromLTRB(0, 0, 0, 5),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(180)
                  ),
                  clipBehavior: Clip.antiAlias,
                  child: Image.asset('assets/images/general/avatar.png'),
                ),
                CustomText(text: UserInfo.user != null ? UserInfo.user['ObjectName'] : '', bold: true, size: 20,)
              ],
            ),
            SizedBox(height: 10),
            renderMenuItem('Thông tin cá nhân', Icons.account_circle, () => Get.to(() => UserInfoView())),
            if (UserInfo.activeLicense)
              renderMenuItem('Áp dụng e-voucher ACB', Icons.discount, () => Get.to(() => ApplyEvoucherView())),
            // renderMenuItem('Quản lý nhân viên', Icons.supervised_user_circle_sharp, () => Get.to(() => UserView())),
            if (UserInfo.activeACB)
              renderMenuItem('Cấu hình thanh toán', Icons.account_balance, () => Get.to(() => BankAccountFormView())),
            renderMenuItem('Cấu hình hoá đơn điện tử', Icons.file_present, () => Get.to(() => EinvoiceConfigView())),
            renderMenuItem('Bảo mật', Icons.security, () => Get.to(() => SecurityView())),
            // renderMenuItem('Bản quyền', Icons.key, () => Get.to(() => LicenseInfoView())),
            renderMenuItem('Đăng xuất', Icons.logout, () {
              final cartController = Get.find<CartSMEController>();
              cartController.clearCart();
              logout();
            }),
          ],
        ),
      ),
    );
  }
}
