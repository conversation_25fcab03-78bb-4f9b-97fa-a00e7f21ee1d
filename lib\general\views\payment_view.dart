import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/controllers/order_controller.dart';
import 'package:gls_self_order/general/controllers/payment_controller.dart';
import 'package:intl/intl.dart';

class PaymentView extends StatefulWidget {
  const PaymentView({super.key});

  @override
  State<PaymentView> createState() => _PaymentViewState();
}

class _PaymentViewState extends State<PaymentView> {
  //variable
  PaymentController paymentController = Get.find();
  OrderController orderController = Get.find();
  TextEditingController searchController = TextEditingController();
  TextEditingController dateFromController = TextEditingController();
  TextEditingController dateToController = TextEditingController();
  List payments = [];
  DateTime today = DateTime.now();
  DateTime dateFrom = DateTime(DateTime.now().year, DateTime.now().month, 1);
  DateTime dateTo = DateTime(DateTime.now().year, DateTime.now().month + 1, 1).subtract(Duration(days: 1));

  //function
  @override
  void initState() {
    super.initState();
    getData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  getData() async {
    AppFunction.showLoading();
    await getList();
    AppFunction.hideLoading();
    setState(() {

    });
  }

  getList() async {
    payments = await paymentController.getList(DateFormat('yyyy-MM-dd').format(dateFrom), DateFormat('yyyy-MM-dd').format(dateTo), 'credit');
  }

  Widget renderFilter() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(5),
      child: Row(
        children: [
          Expanded(
            child: CustomTextField(
              controller: searchController,
              showLabel: false,
              hint: 'Tìm kiếm',
              space: false,
            ),
          ),
          InkWell(
            onTap: () async {
              showFilter(context);
            },
            child: Container(
              width: 45,
              height: 45,
              margin: EdgeInsets.fromLTRB(5, 0, 0, 0),
              decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(10)
              ),
              child: Icon(Icons.search, color: Colors.white,),
            ),
          ),
        ],
      ),
    );
  }

  Widget renderItemPayment(item) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withValues(alpha: 0.05),
              spreadRadius: 0,
              blurRadius: 1,
              offset: Offset(0, 3)
          ),
        ],
      ),
      padding: EdgeInsets.all(10),
      margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(text: 'Mã giao dịch:'),
                    CustomText(text: item['TransCode'] ?? '', bold: true,),
                  ],
                ),
              ),
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(text: 'Thời gian:'),
                    CustomText(text: AppFunction.formatDateWithTime(item['RealTransDate']), bold: true,),
                  ],
                ),
              ),
            ],
          ),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(text: 'Số tiền:'),
                    CustomText(text: AppFunction.formatMoney(item['OrderAmount'] ?? '0'), bold: true,),
                  ],
                ),
              ),
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(text: 'Kênh giao dịch:'),
                    CustomText(text: item['TransChannel'] ?? '', bold: true,),
                  ],
                ),
              ),
            ],
          ),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(text: 'Đơn hàng: '),
                    CustomText(text: item['OrderCode'] ?? 'Đang cập nhật thông tin', bold: true, maxLines: 2,),
                  ],
                ),
              ),
            ],
          ),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(text: 'Chi tiết giao dịch:'),
                    CustomText(text: item['TransactionContent'] ?? '', bold: true, maxLines: 2,),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> selectDate(BuildContext context, String when) async {
    final DateTime? date = await showDatePicker(
      context: context,
      initialDate: today,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (date != null) {
      if (when == 'from') {
        dateFrom = date;
        dateFromController.text = DateFormat('dd/MM/yyyy').format(dateFrom);
      }
      else {
        dateTo = date;
        dateToController.text = DateFormat('dd/MM/yyyy').format(dateTo);
      }
    }
  }

  showFilter(context) {
    dateFromController.text = DateFormat('dd/MM/yyyy').format(dateFrom);
    dateToController.text = DateFormat('dd/MM/yyyy').format(dateTo);
    showGeneralDialog(
        context: context,
        pageBuilder: (context, animation, secondaryAnimation) {
          return Scaffold(
            appBar: AppBar(
              title: CustomTitleAppBar(title: 'Bộ lọc'),
              backgroundColor: AppColors.primary,
              centerTitle: true,
              automaticallyImplyLeading: false,
            ),
            body: SafeArea(
              child: Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10)
                ),
                padding: EdgeInsets.all(10),
                child: Column(
                  children: [
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.zero,
                        children: [
                          CustomTextField(
                            controller: dateFromController,
                            label: 'Từ ngày:',
                            readOnly: true,
                            onTap: () {
                              selectDate(context, 'from');
                            },
                          ),
                          CustomTextField(
                            controller: dateToController,
                            label: 'Đến ngày:',
                            readOnly: true,
                            onTap: () {
                              selectDate(context, 'to');
                            },
                          ),
                        ],
                      ),
                    ),
                    Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: Padding(
                            padding: EdgeInsets.fromLTRB(0, 0, 5, 0),
                            child: CustomButton(
                                text: 'Bỏ lọc',
                                onTap: () {
                                  clearFilter();
                                },
                                color: AppColors.shadow
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Padding(
                            padding: EdgeInsets.fromLTRB(5, 0, 0, 0),
                            child: CustomButton(
                                text: 'Áp dụng',
                                onTap: () {
                                  filter(true);
                                },
                                color: AppColors.primary
                            ),
                          ),
                        )
                      ],
                    )
                  ],
                ),
              ),
            ),
          );
        }
    );
  }

  filter(bool back) async{
    AppFunction.showLoading();
    if (back) Get.back();
    await getList();
    AppFunction.hideLoading();
    setState(() {

    });
  }

  clearFilter() async {
    AppFunction.showLoading();
    dateFrom = DateTime(DateTime.now().year, DateTime.now().month, 1);
    dateTo = DateTime(DateTime.now().year, DateTime.now().month + 1, 1).subtract(Duration(days: 1));
    dateFromController.text = DateFormat('dd/MM/yyyy').format(dateFrom);
    dateToController.text = DateFormat('dd/MM/yyyy').format(dateTo);
    Get.back();
    await getList();
    AppFunction.hideLoading();
    setState(() {

    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Thanh toán'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        automaticallyImplyLeading: false,
        actions: [

        ],
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          children: [
            renderFilter(),
            if (payments.isNotEmpty)
              Expanded(
                child: ListView(
                  padding: EdgeInsets.all(10),
                  children: [
                    for(dynamic item in payments)
                      renderItemPayment(item),
                  ],
                ),
              ),
            if (payments.isEmpty)
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    CustomText(text: 'Chưa có dữ liệu', bold: true,)
                  ],
                ),
              )
          ],
        ),
      ),
    );
  }
}
