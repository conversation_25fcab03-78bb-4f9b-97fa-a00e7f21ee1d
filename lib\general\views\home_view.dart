import 'package:flutter/material.dart';
import 'package:gls_self_order/core/classes/app_function.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_text_field.dart';
import 'package:gls_self_order/core/constants/global_var.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/core/theme/app_images.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/general/classes/user_info.dart';
import 'package:gls_self_order/general/controllers/news_controller.dart';
import 'package:gls_self_order/general/controllers/product_controller.dart';
import 'package:gls_self_order/general/views/account/account_view.dart';
import 'package:gls_self_order/general/views/app_view.dart';
import 'package:gls_self_order/general/views/category_view.dart';
import 'package:gls_self_order/general/views/contact_view.dart';
import 'package:gls_self_order/general/views/finance/finance_view.dart';
import 'package:gls_self_order/general/views/news/news_all_view.dart';
import 'package:gls_self_order/general/views/news/news_detail_view.dart';
import 'package:gls_self_order/general/views/product/product_view.dart';
import 'package:gls_self_order/general/views/warehouse/warehouse_view.dart';

import '../../sme/presentation/e_menu/e_menu_controller.dart';
import '../../sme/presentation/e_menu/e_menu_page.dart';
import '../../sme/routes/sme_app_routes.dart';
import '../controllers/revenue_controller.dart';
import 'report/home/<USER>/home.dart';

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> {
  //variable
  NewsController newsController = Get.find();
  List news = [];
  RevenueController revenueController = Get.find();
  ProductController productController = Get.find();

  //function
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getNews();
    getSaleMenu();
    // Add this to fetch revenue data
    final now = DateTime.now();
    final dateFrom = now.toString().substring(0, 10);
    final dateTo = now.toString().substring(0, 10);
    revenueController.fetchRevenueData(dateFrom: dateFrom, dateTo: dateTo);
  }

  getNews() async {
    news = await newsController.getNews();
    setState(() {

    });
  }

  getSaleMenu() async {
    AppFunction.showLoading();
    await productController.getSaleMenu();
    AppFunction.hideLoading();
  }

  Widget renderItemInSummary(title, value, icon, color) {
    return Expanded(
      flex: 1,
      child: Container(
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8)
        ),
        padding: EdgeInsets.fromLTRB(5, 10, 5, 10),
        margin: EdgeInsets.fromLTRB(2.5, 0, 2.5, 5),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(180)
              ),
              padding: EdgeInsets.all(10),
              margin: EdgeInsets.fromLTRB(0, 0, 2.5, 0),
              child: Icon(icon, color: Colors.white,),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(text: title),
                CustomText(text: value, bold: true,)
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget renderSummary() {
    final now = DateTime.now();
    //final dateFrom = now;
    final dateTo = now;

    return Container(
      padding: EdgeInsets.all(10),
      color: AppColors.primary.withValues(alpha: 0.2),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CustomText(text: 'TỔNG QUAN KINH DOANH', bold: true,),
          Padding(
            padding: EdgeInsets.fromLTRB(0, 0, 0, 5),
            child: CustomText(text: '(${AppFunction.formatDateNoTime(dateTo)})',),
          ),
          Obx(() => Row(
            children: [
              renderItemInSummary('Tổng tiền',
                  AppFunction.formatMoney(revenueController.paymentAmount.value),
                  Icons.attach_money_sharp, Colors.orangeAccent),
              renderItemInSummary('Tiền hàng',
                  AppFunction.formatMoney(revenueController.itemAmount.value),
                  Icons.insert_chart, Colors.cyanAccent),
            ],
          )),
          Obx(() => Row(
            children: [
              renderItemInSummary('Đơn hàng',
                  "${revenueController.totalBill.value}",
                  Icons.file_copy, Colors.blueAccent),
              renderItemInSummary('Giảm giá',
                  AppFunction.formatMoney(revenueController.discountAmount.value),
                  Icons.discount, Colors.greenAccent),
            ],
          )),
          Obx(() => Row(
            children: [
              renderItemInSummary('Hoá đơn điện tử', "${revenueController.totalInvoice.value}",
                  Icons.file_present, Colors.lightGreen),
              renderItemInSummary('Tiền VAT',
                  AppFunction.formatMoney(revenueController.vatAmount.value),
                  Icons.money, Colors.cyan),
            ],
          ))
        ],
      ),
    );
  }

  Widget renderItemMainButton(String title, String image, dynamic view) {
    return SizedBox(
      width: 110,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () {
              if (view is String) {
                Get.toNamed(view);
              } else if (view is Widget) {
                Get.to(() => view);
              } else {
                throw Exception('Invalid type passed to renderItemMainButton');
              }
            },
            child: Container(
              width: 110,
              height: 100,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              padding: EdgeInsets.all(10),
              margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(image, width: 50),
                  CustomText(text: title, bold: true),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget renderMainButton() {
    return Container(
      padding: EdgeInsets.all(10),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(10),
            child: CustomText(text: 'Ứng dụng', bold: true, size: 20,),
          ),
          Wrap(
            spacing: 20,
            runSpacing: 5,
            children: [
              renderItemMainButton('Bán hàng', 'assets/images/general/ban_hang.png', SmeAppRoutes.menu),
              renderItemMainButton('Tài chính', 'assets/images/general/tai_chinh.png', FinanceView()),
              renderItemMainButton('Danh mục', 'assets/images/general/vi_voucher.png', CategoryView()),
              renderItemMainButton('Doanh thu', 'assets/images/general/doanh_thu.png', ReportView()),
            ],
          ),

          // Row(
          //   children: [
          //     renderItemMainButton('Bán hàng', 'assets/images/general/ban_hang.png', SmeAppRoutes.menu),
          //     renderItemMainButton('Tài chính', 'assets/images/general/tai_chinh.png', FinanceView()),
          //     renderItemMainButton('Danh mục', 'assets/images/general/vi_voucher.png', CategoryView()),
          //   ],
          // ),
          // Row(
          //   children: [
          //     renderItemMainButton('Doanh thu', 'assets/images/general/doanh_thu.png', ReportView()),
          //     // renderItemMainButton('Tài chính', 'assets/images/general/tai_chinh.png', FinanceView()),
          //     // renderItemMainButton('Khuyến mãi', 'assets/images/general/vi_voucher.png', ContactView()),
          //     // renderItemMainButton('Quản lý kho', 'assets/images/general/quan_ly_kho.png', WarehouseView()),
          //   ],
          // ),
        ],
      ),
    );
  }

  Widget renderItemNews(item) {
    return InkWell(
      onTap: () {
        Get.to(() => NewsDetailView(id: item['ID']));
      },
      child: Container(
        width: 180,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 5,
              offset: Offset(0, 2),
            ),
          ],
        ),
        margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
        clipBehavior: Clip.antiAlias,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
                width: 180,
                height: 180,
                child: item['CoverImageURL'].isNotEmpty
                    ? Image.network(item['CoverImageURL'], fit: BoxFit.cover,)
                    : Image.asset('assets/images/general/no_image.jpg')
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(180),
                        border: Border.all(
                            color: item['ContentType'] == 1 ? AppColors.primary : AppColors.button,
                            width: 1
                        )
                    ),
                    padding: EdgeInsets.fromLTRB(7, 2.5, 7, 2.5),
                    margin: EdgeInsets.fromLTRB(0, 5, 0, 0),
                    child: CustomText(text: item['ContentType'] == 1 ? 'Tin tức' : 'Khuyến mãi', color: item['ContentType'] == 1 ? AppColors.primary : AppColors.button,),
                  ),
                  CustomText(text: item['Title'], maxLines: 2, bold: true,),
                  Row(
                    children: [
                      Icon(Icons.calendar_month),
                      Expanded(child: CustomText(text: AppFunction.formatDateNoTime(item['UpdatedDate'])))
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget renderNews() {
    return Container(
      padding: EdgeInsets.all(10),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: CustomText(text: 'Tin tức - Khuyến mãi', bold: true, size: 20,),
              ),
              InkWell(
                onTap: () {
                  Get.to(() => NewsAllView());
                },
                child: Container(
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(180),
                      border: Border.all(
                          color: AppColors.primary,
                          width: 2
                      )
                  ),
                  padding: EdgeInsets.fromLTRB(5, 0, 5, 0),
                  child: CustomText(text: 'Xem tất cả', color: AppColors.primary, size: 14,),
                ),
              )
            ],
          ),
          Container(
            width: double.infinity,
            // color: Colors.blue,
            height: 310,
            margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
            child: ListView(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.fromLTRB(2, 0, 0, 10),
              children: [
                for(dynamic item in news)
                  renderItemNews(item),
              ],
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;

    return Scaffold(
      appBar: AppBar(
        title: Padding(
            padding: EdgeInsets.fromLTRB(0, 0, 0, 5),
            child: DecoratedBox(
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8)
              ),
              child: DropdownButton(
                value: UserInfo.branchId,
                items: [
                  for (dynamic item in UserInfo.branches)
                    DropdownMenuItem(
                      value: item['BranchId'],
                      child: CustomText(text: item['BranchName']),
                    )
                ],
                onChanged: (value) {
                  UserInfo.branchId = int.parse(value.toString());
                  setState(() {

                  });
                  final now = DateTime.now();
                  final date = now.toString().substring(0, 10);
                  revenueController.fetchRevenueData(dateFrom: date, dateTo: date);
                },
                underline: Container(),
                isExpanded: true,
                padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
              ),
            )
        ),
        backgroundColor: AppColors.primary,
        automaticallyImplyLeading: false,
        // leading: InkWell(
        //   onTap: () {
        //
        //   },
        //   child: Container(
        //     decoration: BoxDecoration(
        //       borderRadius: BorderRadius.circular(180)
        //     ),
        //     margin: EdgeInsets.fromLTRB(5, 0, 0, 5),
        //     clipBehavior: Clip.antiAlias,
        //     child: Image.asset('assets/images/general/assistant.jpg', width: 50, fit: BoxFit.cover,),
        //   ),
        // ),
        actions: [
          Padding(
            padding: EdgeInsets.fromLTRB(0, 0, 5, 5),
            child: InkWell(
              onTap: () {
                Get.to(() => AccountView(from: 'avatar',));
              },
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(180)
                ),
                // margin: EdgeInsets.all(5),
                clipBehavior: Clip.antiAlias,
                child: Image.asset('assets/images/general/avatar.png', width: 50, fit: BoxFit.cover),
              ),
            ),
          )
        ],
      ),
      body: ListView(
        padding: EdgeInsets.zero,
        children: [
          //summary
          renderSummary(),
          Column(
            children: [
              Container(
                width: width > GlobalVar.maxWidth ? GlobalVar.maxWidth : double.infinity,
                child: renderMainButton(),
              ),
            ],
          ),
          renderNews(),
          // SizedBox(height: 10,)
        ],
      ),
    );
  }
}
