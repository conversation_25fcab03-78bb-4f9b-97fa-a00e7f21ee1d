import 'package:flutter/material.dart';
import 'package:gls_self_order/core/components/custom_text.dart';

class OrderRowInfo extends StatelessWidget {
  final String label;
  final String value;
  final bool bold;
  final Color? color;
  final int? maxLines;
  

  const OrderRowInfo({
    super.key,
    required this.label,
    required this.value,
    this.bold = true,
    this.color,
    this.maxLines = 1,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(text: label),
        Expanded(
          child: CustomText(
            text: value,
            bold: bold,
            color: color ?? Colors.black,
            maxLines: maxLines,
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }
}
