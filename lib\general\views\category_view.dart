import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gls_self_order/core/components/custom_back_button.dart';
import 'package:gls_self_order/core/components/custom_text.dart';
import 'package:gls_self_order/core/components/custom_title_app_bar.dart';
import 'package:gls_self_order/core/theme/app_colors.dart';
import 'package:gls_self_order/general/views/customer/customer_view.dart';
import 'package:gls_self_order/general/views/manufacture/manufacture_view.dart';
import 'package:gls_self_order/general/views/product/product_view.dart';

class CategoryView extends StatefulWidget {
  const CategoryView({super.key});

  @override
  State<CategoryView> createState() => _CategoryViewState();
}

class _CategoryViewState extends State<CategoryView> {
  //variable

  //function
  Widget renderMenuItem(title, icon, VoidCallback view) {
    return InkWell(
      onTap: view,
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: AppColors.primary.withValues(alpha: 0.1)
        ),
        padding: EdgeInsets.all(10),
        margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(180)
              ),
              margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
              child: Icon(icon, size: 30, color: AppColors.primary,),
            ),
            Expanded(
              child: CustomText(text: title, size: 18, bold: true,),
            )
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomTitleAppBar(title: 'Quản lý danh mục'),
        backgroundColor: AppColors.primary,
        centerTitle: true,
        leading: CustomBackButton(),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        // color: Colors.white,
        padding: EdgeInsets.all(10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            renderMenuItem('Khách hàng', Icons.people, () => Get.to(() => CustomerView())),
            renderMenuItem('Sản phẩm', Icons.fastfood, () => Get.to(() => ProductView())),
            // renderMenuItem('Nhà cung cấp', Icons.storefront, () => Get.to(() => ManufactureView())),
          ],
        ),
      ),
    );
  }
}
